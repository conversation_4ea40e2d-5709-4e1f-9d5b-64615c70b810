package cn.guliandigital.web.controller.api.product;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.service.ITProBooksService;
import cn.guliandigital.product.book.vo.ClassTreeType;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 图书平台API控制器
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/book/platform")
public class ApiBookPlatformController extends BaseController {

    @Autowired
    private ITProBooksService tProBooksService;

    @Autowired
    private ServerConfig serverConfig;

    @Value("${huanghe.downloadPath}")
    private String downloadPath;

    /**
     * 查询图书平台列表
     * 支持按黄河大系分类筛选、全文筛选、分页（每页12本）
     */
    @GetMapping("/list")
    public TableDataInfo list(TProBooks tProBooks,
                              @RequestParam(value = "resourceClassesId", required = false) String resourceClassesId,
                              @RequestParam(value = "fullTextOnly", required = false, defaultValue = "false") Boolean fullTextOnly) {
        
        // 设置分页参数，每页12本
        if (getPageNum() == null) {
            setPageNum(1);
        }
        if (getPageSize() == null) {
            setPageSize(12);
        }
        
        startPage();
        
        // 处理分类筛选
        if (!StringUtil.isEmpty(resourceClassesId)) {
            tProBooks.setResourceClassesId(resourceClassesId);
        }
        
        // 处理全文筛选
        if (fullTextOnly != null && fullTextOnly) {
            tProBooks.setResourceType("T"); // T表示全文
        }
        
        // 只查询已上架的图书
        tProBooks.setProStatus(1);
        
        List<TProBooks> list = tProBooksService.selectBookPlatformList(tProBooks);
        
        // 处理图片URL
        String url = serverConfig.getUrl();
        for (TProBooks book : list) {
            if (!Strings.isNullOrEmpty(book.getCoverUrl())) {
                if (!book.getCoverUrl().startsWith("http")) {
                    book.setCoverUrl(url + downloadPath + book.getCoverUrl());
                }
            }
            if (!Strings.isNullOrEmpty(book.getThumbCoverUrl())) {
                if (!book.getThumbCoverUrl().startsWith("http")) {
                    book.setThumbCoverUrl(url + downloadPath + book.getThumbCoverUrl());
                }
            }
        }
        
        return getDataTable(list);
    }

    /**
     * 获取黄河大系12卷分类及对应图书数量
     */
    @GetMapping("/categories")
    public AjaxResult getCategories() {
        List<ClassTreeType> categories = tProBooksService.selectHuangHeClassesWithCount();
        return AjaxResult.success(categories);
    }

    /**
     * 获取图书详情
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        TProBooks book = tProBooksService.selectBookPlatformDetail(id);
        if (book == null) {
            return AjaxResult.error("图书不存在");
        }
        
        // 处理图片URL
        String url = serverConfig.getUrl();
        if (!Strings.isNullOrEmpty(book.getCoverUrl())) {
            if (!book.getCoverUrl().startsWith("http")) {
                book.setCoverUrl(url + downloadPath + book.getCoverUrl());
            }
        }
        if (!Strings.isNullOrEmpty(book.getThumbCoverUrl())) {
            if (!book.getThumbCoverUrl().startsWith("http")) {
                book.setThumbCoverUrl(url + downloadPath + book.getThumbCoverUrl());
            }
        }
        
        return AjaxResult.success(book);
    }

    /**
     * 获取全文图书统计数量
     */
    @GetMapping("/fulltext/count")
    public AjaxResult getFullTextCount() {
        TProBooks query = new TProBooks();
        query.setResourceType("T");
        query.setProStatus(1);
        
        List<TProBooks> list = tProBooksService.selectBookPlatformList(query);
        return AjaxResult.success("data", list.size());
    }
}
