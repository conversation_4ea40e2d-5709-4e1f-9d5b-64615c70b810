package cn.guliandigital.web.controller.api.product;

import cn.guliandigital.common.core.controller.BaseController;
import cn.guliandigital.common.core.domain.AjaxResult;
import cn.guliandigital.common.core.page.TableDataInfo;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.framework.config.ServerConfig;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.service.ITProBooksService;
import cn.guliandigital.product.book.vo.ClassTreeType;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.Map;

/**
 * 图书平台API控制器
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/book/platform")
public class BookPlatformController extends BaseController {

    @Autowired
    private ITProBooksService tProBooksService;

    @Autowired
    private ITConfigClassicTreeService tConfigClassicTreeService;

    @Autowired
    private ServerConfig serverConfig;

    @Value("${huanghe.downloadPath}")
    private String downloadPath;

    /**
     * 查询图书平台列表
     * 支持按黄河大系分类筛选、全文筛选、分页（每页12本）
     */
    @GetMapping("/list")
    public TableDataInfo list(TProBooks tProBooks,
                              @RequestParam(value = "resourceClassesId", required = false) String resourceClassesId,
                              @RequestParam(value = "fullTextOnly", required = false, defaultValue = "false") Boolean fullTextOnly) {
        
        // 设置分页参数，每页12本
        if (getPageNum() == null) {
            setPageNum(1);
        }
        if (getPageSize() == null) {
            setPageSize(12);
        }
        
        startPage();
        
        // 处理分类筛选
        if (!StringUtil.isEmpty(resourceClassesId)) {
            tProBooks.setResourceClassesId(resourceClassesId);
        }
        
        // 处理全文筛选
        if (fullTextOnly != null && fullTextOnly) {
            tProBooks.setResourceType("T"); // T表示全文
        }
        
        // 只查询已上架的图书
        tProBooks.setProStatus(1);
        
        // 使用现有的查询方法
        List<TProBooks> list = tProBooksService.selectTProBooksList(tProBooks);
        
        // 处理图片URL
        String url = serverConfig.getUrl();
        for (TProBooks book : list) {
            if (!Strings.isNullOrEmpty(book.getCoverUrl())) {
                if (!book.getCoverUrl().startsWith("http")) {
                    book.setCoverUrl(url + downloadPath + book.getCoverUrl());
                }
            }
            if (!Strings.isNullOrEmpty(book.getThumbCoverUrl())) {
                if (!book.getThumbCoverUrl().startsWith("http")) {
                    book.setThumbCoverUrl(url + downloadPath + book.getThumbCoverUrl());
                }
            }
        }
        
        return getDataTable(list);
    }

    /**
     * 获取黄河大系12卷分类及对应图书数量
     */
    @GetMapping("/categories")
    public AjaxResult getCategories() {
        // 查询黄河大系分类
        TConfigClassicTree query = new TConfigClassicTree();
        List<TConfigClassicTree> categories = tConfigClassicTreeService.selectTConfigClassicTreeList(query);
        
        // 过滤出黄河大系相关分类（这里需要根据实际数据结构调整）
        List<TConfigClassicTree> huangHeCategories = categories.stream()
            .filter(cat -> cat.getTreeName() != null && 
                   (cat.getTreeName().contains("黄河") || cat.getTreeName().contains("大系")))
            .collect(Collectors.toList());
        
        // 统计每个分类的图书数量
        for (TConfigClassicTree category : huangHeCategories) {
            TProBooks countQuery = new TProBooks();
            countQuery.setResourceClassesId(category.getId());
            countQuery.setProStatus(1);
            List<TProBooks> books = tProBooksService.selectTProBooksList(countQuery);
            // 这里可以添加数量统计逻辑
        }
        
        return AjaxResult.success(huangHeCategories);
    }

    /**
     * 获取图书详情
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        TProBooks book = tProBooksService.selectTProBooksById(id);
        if (book == null) {
            return AjaxResult.error("图书不存在");
        }
        
        // 处理图片URL
        String url = serverConfig.getUrl();
        if (!Strings.isNullOrEmpty(book.getCoverUrl())) {
            if (!book.getCoverUrl().startsWith("http")) {
                book.setCoverUrl(url + downloadPath + book.getCoverUrl());
            }
        }
        if (!Strings.isNullOrEmpty(book.getThumbCoverUrl())) {
            if (!book.getThumbCoverUrl().startsWith("http")) {
                book.setThumbCoverUrl(url + downloadPath + book.getThumbCoverUrl());
            }
        }
        
        return AjaxResult.success(book);
    }

    /**
     * 获取全文图书统计数量
     */
    @GetMapping("/fulltext/count")
    public AjaxResult getFullTextCount() {
        TProBooks query = new TProBooks();
        query.setResourceType("T");
        query.setProStatus(1);
        
        List<TProBooks> list = tProBooksService.selectTProBooksList(query);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", list.size());
        
        return AjaxResult.success(result);
    }

    /**
     * 获取图书统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总图书数量
        TProBooks totalQuery = new TProBooks();
        totalQuery.setProStatus(1);
        List<TProBooks> totalBooks = tProBooksService.selectTProBooksList(totalQuery);
        statistics.put("totalBooks", totalBooks.size());
        
        // 全文图书数量
        TProBooks fullTextQuery = new TProBooks();
        fullTextQuery.setResourceType("T");
        fullTextQuery.setProStatus(1);
        List<TProBooks> fullTextBooks = tProBooksService.selectTProBooksList(fullTextQuery);
        statistics.put("fullTextBooks", fullTextBooks.size());
        
        // PDF图书数量
        TProBooks pdfQuery = new TProBooks();
        pdfQuery.setResourceType("PDF");
        pdfQuery.setProStatus(1);
        List<TProBooks> pdfBooks = tProBooksService.selectTProBooksList(pdfQuery);
        statistics.put("pdfBooks", pdfBooks.size());
        
        return AjaxResult.success(statistics);
    }

    /**
     * 获取黄河大系分类树（简化版本）
     */
    @GetMapping("/categories/tree")
    public AjaxResult getCategoriesTree() {
        // 使用现有的分类查询方法
        ClassTreeType type = new ClassTreeType();
        List<ClassTreeType> categories = tProBooksService.selectType(type);
        
        // 构建树形结构
        List<ClassTreeType> rootCategories = categories.stream()
            .filter(cat -> StringUtil.isEmpty(cat.getTreePid()))
            .collect(Collectors.toList());
        
        for (ClassTreeType rootCategory : rootCategories) {
            tProBooksService.getList(rootCategory, categories, rootCategory.getId());
        }
        
        return AjaxResult.success(rootCategories);
    }

    /**
     * 根据分类ID获取图书数量
     */
    @GetMapping("/categories/{categoryId}/count")
    public AjaxResult getCategoryBookCount(@PathVariable("categoryId") String categoryId) {
        TProBooks query = new TProBooks();
        query.setResourceClassesId(categoryId);
        query.setProStatus(1);
        
        List<TProBooks> books = tProBooksService.selectTProBooksList(query);
        
        Map<String, Object> result = new HashMap<>();
        result.put("categoryId", categoryId);
        result.put("count", books.size());
        
        return AjaxResult.success(result);
    }
}
