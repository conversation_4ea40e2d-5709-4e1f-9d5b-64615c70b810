# 图书平台API接口文档

## 概述

图书平台提供了一套完整的后端API接口，支持图书展示、分类筛选、全文阅读筛选等功能。

## 基础信息

- **基础路径**: `/api/book/platform`
- **返回格式**: JSON
- **分页大小**: 每页12本图书

## 接口列表

### 1. 获取图书列表

**接口地址**: `GET /api/book/platform/list`

**功能描述**: 查询图书平台列表，支持分类筛选、全文筛选、分页

**请求参数**:
- `pageNum` (可选): 页码，默认1
- `pageSize` (可选): 每页数量，默认12
- `resourceClassesId` (可选): 黄河大系分类ID
- `fullTextOnly` (可选): 是否仅显示全文图书，默认false
- `bookName` (可选): 图书名称关键词搜索

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": "book123",
      "bookName": "黄河文化研究",
      "resourceType": "T",
      "coverUrl": "http://example.com/cover.jpg",
      "mainResponsibility": "张三",
      "publisher": "人民出版社",
      "publishDate": "2023-01-01"
    }
  ],
  "total": 120
}
```

### 2. 获取黄河大系分类

**接口地址**: `GET /api/book/platform/categories`

**功能描述**: 获取黄河大系12卷分类及对应图书数量

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": "cat001",
      "treeName": "典籍编",
      "bookCount": 25
    },
    {
      "id": "cat002", 
      "treeName": "研究编",
      "bookCount": 30
    }
  ]
}
```

### 3. 获取图书详情

**接口地址**: `GET /api/book/platform/{id}`

**功能描述**: 根据图书ID获取详细信息

**路径参数**:
- `id`: 图书ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": "book123",
    "bookName": "黄河文化研究",
    "resourceType": "T",
    "coverUrl": "http://example.com/cover.jpg",
    "bookDesc": "详细描述...",
    "mainResponsibility": "张三",
    "publisher": "人民出版社",
    "publishDate": "2023-01-01",
    "wordNum": 50000,
    "picNum": 100
  }
}
```

### 4. 获取全文图书数量

**接口地址**: `GET /api/book/platform/fulltext/count`

**功能描述**: 获取全文阅读图书的统计数量

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "count": 85
  }
}
```

### 5. 获取图书统计信息

**接口地址**: `GET /api/book/platform/statistics`

**功能描述**: 获取图书平台的整体统计信息

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "totalBooks": 500,
    "fullTextBooks": 85,
    "pdfBooks": 415
  }
}
```

## 使用说明

### 图书筛选

1. **按分类筛选**: 使用 `resourceClassesId` 参数
2. **全文筛选**: 设置 `fullTextOnly=true`
3. **关键词搜索**: 使用 `bookName` 参数

### 分页说明

- 默认每页显示12本图书
- 使用 `pageNum` 和 `pageSize` 参数控制分页
- 响应中包含 `total` 字段表示总数量

### 图书类型说明

- `resourceType = "T"`: 全文阅读
- `resourceType = "PDF"`: PDF立体阅读
- `proStatus = 1`: 已上架图书

## 错误码说明

- `200`: 成功
- `500`: 服务器内部错误
- `404`: 资源不存在
