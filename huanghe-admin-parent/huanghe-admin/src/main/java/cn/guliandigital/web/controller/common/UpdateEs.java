//package cn.guliandigital.web.controller.common;
//
//import cn.guliandigital.es.domain.TFullQueryEsBooks;
//import cn.guliandigital.es.domain.TFullQueryEsMenus;
//import cn.guliandigital.es.domain.TFullQueryEsMenusSynonyms;
//import cn.guliandigital.es.service.IElasticService;
//import cn.guliandigital.product.book.domain.TProBooks;
//import cn.guliandigital.product.book.service.ITProBooksService;
//import com.google.common.collect.Maps;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Map;
//
//@Component
//@Slf4j
//public class UpdateEs implements CommandLineRunner {
//    @Autowired
//    private ITProBooksService tProBooksService;
//    @Autowired
//    private ElasticsearchTemplate elasticsearchTemplate;
//    @Autowired
//    private IElasticService elasticsearchService;
//
//    @Override
//    public void run(String... args) throws Exception {
//        log.info("开始执行es更新--------");
//        TProBooks tProBooks = new TProBooks();
//        List<TProBooks> tProBooks1 = tProBooksService.selectTProBooksList(tProBooks);
//        log.info("查询出来所有上架的数据======"+tProBooks1+"=====");
//        elasticsearchTemplate.refresh(TFullQueryEsBooks.class);
//        elasticsearchTemplate.refresh(TFullQueryEsMenus.class);
//        elasticsearchTemplate.refresh(TFullQueryEsMenusSynonyms.class);
//        Map<String, Object> condition = Maps.newHashMap();
//        for (TProBooks book : tProBooks1) {
//            String id = book.getId();
//            log.info("图书id为:"+id+"----");
//            // 更新es中状态
//            condition.clear();
//            condition.put("bookId", book.getId());
//            // condition.put("dataStatus", "0");////状态 0-下架 1-上架
//            List<TFullQueryEsMenus> eslist = elasticsearchService.getIdListMenus(condition);
//            for (TFullQueryEsMenus es : eslist) {
//                log.info("==>es:{}", es.getDataStatus()+"---menu---");
//                es.setDataStatus(book.getProStatus().toString());
//                log.info("==>es:{}", es.getDataStatus()+"---menu---");
//            }
//            elasticsearchService.bulkUpdateDataMenus(eslist);
//
//            condition.put("bookId", book.getId());
//            // condition.put("dataStatus", "0");////状态 0-下架 1-上架
//            List<TFullQueryEsMenusSynonyms> esSynonymslist = elasticsearchService.getIdListMenusSynonyms(condition);
//            for (TFullQueryEsMenusSynonyms es : esSynonymslist) {
//                log.info("==>es:{}", es.getDataStatus()+"---Synonyms---");
//                es.setDataStatus(book.getProStatus().toString());
//                log.info("==>es:{}", es.getDataStatus()+"---Synonyms---");
//            }
//            elasticsearchService.bulkUpdateDataMenusSynonyms(esSynonymslist);
//
//            condition.put("bookId", book.getId());
//            // condition.put("dataStatus", "0");////状态 0-下架 1-上架
//            List<TFullQueryEsBooks> bookEslist = elasticsearchService.getIdListBooks(condition);
//            for (TFullQueryEsBooks es : bookEslist) {
//                log.info("==>es:{}", es.getDataStatus());
//                es.setDataStatus(book.getProStatus().toString());
//                log.info("==>es:{}", es.getDataStatus());
//            }
//            elasticsearchService.bulkUpdateDataBooks(bookEslist);
//        }
//        log.info("更新完毕----------------");
//    }
//}
