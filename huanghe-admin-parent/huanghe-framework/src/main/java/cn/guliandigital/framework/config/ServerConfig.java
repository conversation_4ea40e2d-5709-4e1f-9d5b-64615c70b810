package cn.guliandigital.framework.config;

import javax.servlet.http.HttpServletRequest;

import cn.guliandigital.common.config.HuangHeConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cn.guliandigital.common.utils.ServletUtils;
import cn.guliandigital.common.utils.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 服务相关配置
 * 
 * <AUTHOR>
 */

@Slf4j
@Data
@Component
public class ServerConfig
{

    public  String getPathUrl() {
        return pathUrl;
    }

    
    @Value("${server.servlet.context-path}")
    public void setPathUrl(String pathUrl) {
        this.pathUrl = pathUrl;
    }

    @Value("${server.servlet.context-path}")
    private static String  pathUrl;
    
    @Autowired
    private HuangHeConfig qiluwenkuConfig;

    /**
     * 获取完整的请求路径，包括：域名，端口，上下文访问路径
     * 
     * @return 服务地址
     */
    public String getUrl()
    {
        HttpServletRequest request = ServletUtils.getRequest();        
        return getDomain(request);        
    }

    public static String getDomain(HttpServletRequest request)
    {
        StringBuffer url = request.getRequestURL();
        String contextPath = request.getServletContext().getContextPath();
        String uri = request.getRequestURI();
        //log.info("url={},contextPath={},uri={}",url,contextPath,uri);
       
        
        //String domain = url.delete(url.length() - uri.length(), url.length()).append(contextPath).toString();
        String domain = url.delete(url.length() - uri.length(), url.length()).append("").toString();
        

        if(!StringUtil.endsWith(domain,pathUrl) ){
            domain = domain+pathUrl;
        }
        HuangHeConfig.setSslEnabled(true);
        //log.info("==>ssl：{}",HuangHeConfig.isSslEnabled());
        if(HuangHeConfig.isSslEnabled()) {
        	//log.info("==>ssl：{}",HuangHeConfig.isSslEnabled());
        	if(!StringUtils.startsWith(domain, "https:")) {
        		domain = StringUtils.replace(domain, "http:", "https:");
        	}
        }
        //log.info("==>请求的domain：{}",domain);
        return domain;
    }
}
