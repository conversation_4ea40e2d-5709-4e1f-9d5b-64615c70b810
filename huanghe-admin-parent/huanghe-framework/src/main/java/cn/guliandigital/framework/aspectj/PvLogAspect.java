package cn.guliandigital.framework.aspectj;

import java.lang.reflect.Method;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import com.alibaba.fastjson.JSON;

import cn.guliandigital.common.annotation.PvLog;
import cn.guliandigital.common.enums.BusinessStatus;
import cn.guliandigital.common.enums.HttpMethod;
import cn.guliandigital.common.utils.ServletUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.ip.IpUtils;
import cn.guliandigital.framework.config.IPDataHandler;
import cn.guliandigital.framework.manager.AsyncPvManager;
import cn.guliandigital.framework.manager.factory.AsyncPvFactory;
import cn.guliandigital.plat.domain.TPlatOrganIp;
import cn.guliandigital.plat.domain.TPlatUser;
import cn.guliandigital.plat.service.ITPlatOrganIpService;
import cn.guliandigital.pvlog.domain.TUserVisitLog;
import cn.guliandigital.session.util.SecurityPlatuserUtils;
import cn.guliandigital.system.domain.SysOperLog;

/**
 * 操作日志记录处理
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class PvLogAspect
{
    private static final Logger log = LoggerFactory.getLogger(PvLogAspect.class);

    @Autowired
    private ITPlatOrganIpService tPlatOrganIpService;
    
    @Autowired
    private SecurityPlatuserUtils securityPlatuserUtils;

    
    // 配置织入点
    @Pointcut("@annotation(cn.guliandigital.common.annotation.PvLog)")
    public void logPointCut()
    {
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "logPointCut()", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Object jsonResult)
    {
        handleLog(joinPoint, null, jsonResult);
    }

    /**
     * 拦截异常操作
     * 
     * @param joinPoint 切点
     * @param e 异常
     */
    @AfterThrowing(value = "logPointCut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e)
    {
        handleLog(joinPoint, e, null);
    }

    protected void handleLog(final JoinPoint joinPoint, final Exception e, Object jsonResult)
    {
        try
        {
            // 获得注解
            PvLog controllerLog = getAnnotationLog(joinPoint);
            if (controllerLog == null)
            {
                return;
            }

            // 获取当前的用户
            //LoginUser user = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            TPlatUser user = null;
            try {
            	user = securityPlatuserUtils.getUser(ServletUtils.getRequest());
            }catch(Exception e1) {
            	
            }
            // *========数据库日志=========*//
            TUserVisitLog operLog = new TUserVisitLog();
            
            operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            // 请求的地址
            String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
            operLog.setOperIp(ip);
            operLog.setOperLocation(IPDataHandler.findGeography(operLog.getOperIp()));
            //获取机构信息
            TPlatOrganIp tPlatOrganIp = tPlatOrganIpService.selectOrganbyIp(ip);
            if(tPlatOrganIp != null){
                String orgId = tPlatOrganIp.getOrgId();
                operLog.setOrgId(orgId);
            }
            // 返回参数
            //operLog.setJsonResult(JSON.toJSONString(jsonResult));
           // operLog.setUserSource(UserSource.PC.getCode());
            //log.info("==>用户类型 {}", operLog.getUserSource());
            
//            if(user == null || Strings.isNullOrEmpty(user.getOrgId())){
//            	if(!Strings.isNullOrEmpty(operLog.getOrgId())) {
//            		operLog.setLoginForm(LoginForm.O.getCode());
//            	}else {
//            		operLog.setLoginForm(LoginForm.U.getCode());
//            	}
//            }else {
//            	operLog.setLoginForm(LoginForm.O.getCode());
//            }
            
            operLog.setOperUrl(ServletUtils.getRequest().getRequestURI());
            if (user != null)
            {
                operLog.setOperName(user.getUserName());
            }

            if (e != null)
            {
                operLog.setStatus(BusinessStatus.FAIL.ordinal());
                operLog.setErrorMsg(StringUtil.substring(e.getMessage(), 0, 2000));
            }
//            // 设置方法名称
//            String className = joinPoint.getTarget().getClass().getName();
//            String methodName = joinPoint.getSignature().getName();
//            
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, controllerLog, operLog);
//            if(Strings.isNullOrEmpty(operLog.getUserSource())) {
//            	operLog.setUserSource(UserSource.PC.getCode());
//            }
            // 保存数据库
            AsyncPvManager.me().execute(AsyncPvFactory.recordPvLog(operLog));
        }
        catch (Exception e1)
        {
            // 记录本地异常日志            
            log.error("异常信息:{}", e1);
            
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     * 
     * @param log 日志
     * @param operLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, PvLog log, TUserVisitLog operLog) throws Exception
    {
        // 设置action动作
        operLog.setBusinessType(log.businessType().ordinal());
        operLog.setUserSource(log.userSource().getCode());
        // 设置标题
        operLog.setTitle(log.title());
        // 设置操作人类别
        //operLog.setOperatorType(log.operatorType().ordinal());
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData())
        {
            // 获取参数的信息，传入到数据库中。
            //setRequestValue(joinPoint, operLog);
        }
    }

    /**
     * 获取请求的参数，放到log中
     * 
     * @param operLog 操作日志
     */
    private void setRequestValue(JoinPoint joinPoint, SysOperLog operLog) {
        String requestMethod = operLog.getRequestMethod();
        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod))
        {
            String params = argsArrayToString(joinPoint.getArgs());
            operLog.setOperParam(StringUtil.substring(params, 0, 2000));
        }
        else
        {
            Map<?, ?> paramsMap = (Map<?, ?>) ServletUtils.getRequest().getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
            operLog.setOperParam(StringUtil.substring(paramsMap.toString(), 0, 2000));
        }
    }

    /**
     * 是否存在注解，如果存在就获取
     */
    private PvLog getAnnotationLog(JoinPoint joinPoint) {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        if (method != null)
        {
            return method.getAnnotation(PvLog.class);
        }
        return null;
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray)
    {
        StringBuilder params = new StringBuilder();
        if (paramsArray != null && paramsArray.length > 0)
        {
            for (int i = 0; i < paramsArray.length; i++)
            {
                if (!isFilterObject(paramsArray[i]))
                {
                    Object jsonObj = JSON.toJSON(paramsArray[i]);
                    params.append(jsonObj.toString()).append(" ");
                }
            }
        }
        return params.toString().trim();
    }

    /**
     * 判断是否需要过滤的对象。
     * 
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    public boolean isFilterObject(final Object o)
    {
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse;
    }
}
