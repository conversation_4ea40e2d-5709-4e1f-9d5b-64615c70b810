<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.order.mapper.TProOrderMapper">
	<cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TProOrder" id="TProOrderResult">
        <result property="id" column="ID"/>
        <result property="orderCode" column="ORDER_CODE"/>
        <result property="orderCreateName" column="ORDER_CREATENAME"/>
        <result property="orgId" column="ORG_ID"/>
        <result property="ktDatabase" column="KT_DATABASE"/>
        <result property="orderStatus" column="ORDER_STATUS"/>
        <result property="price" column="PRICE"/>
        <result property="discount" column="DISCOUNT"/>
        <result property="finalPrice" column="FINAL_PRICE"/>
        <result property="authMethod" column="AUTH_METHOD"/>
        <result property="authStartTime" column="AUTH_START_TIME"/>
        <result property="authEndTime" column="AUTH_END_TIME"/>
        <result property="orderDesc" column="ORDER_DESC"/>
        <result property="createbyId" column="CREATEBY_ID"/>
        <result property="createbyName" column="CREATEBY_NAME"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updatebyId" column="UPDATEBY_ID"/>
        <result property="updatebyName" column="UPDATEBY_NAME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="delFlag" column="DEL_FLAG"/>
        <result property="orgName" column="ORG_NAME"/>
        <result property="orderType" column="ORDER_TYPE"/>
    </resultMap>

    <sql id="selectTProOrderVo">
        select p.ID, ORDER_TYPE, ORDER_CODE, ORG_ID, KT_DATABASE, ORDER_STATUS, PRICE, DISCOUNT, FINAL_PRICE, AUTH_METHOD, AUTH_START_TIME, AUTH_END_TIME, ORDER_DESC, p.CREATEBY_ID, p.CREATEBY_NAME, p.CREATE_TIME, p.UPDATEBY_ID, p.UPDATEBY_NAME, p.UPDATE_TIME
    </sql>



    <select id="selectTProOrderList" parameterType="TProOrder" resultMap="TProOrderResult">

        select p.ID,p.ORDER_CREATENAME, p.ORDER_CODE, p.ORG_ID, p.KT_DATABASE, p.ORDER_STATUS, p.PRICE, p.DISCOUNT,
        p.FINAL_PRICE, p.AUTH_METHOD, p.AUTH_START_TIME,p. AUTH_END_TIME, ORDER_DESC, p.CREATEBY_ID, p.CREATEBY_NAME,
        p.CREATE_TIME, p.UPDATEBY_ID, p.UPDATEBY_NAME, p.UPDATE_TIME, p.ORDER_TYPE
        ,o.ORG_NAME
        from t_pro_order p
        left join t_plat_organ o
        on p.ORG_ID = o.ID
        where
        p.DEL_FLAG !=1
        and o.ORG_STATUS = '0'
            <if test="orgId != null  and orgId != ''">and ORG_ID = #{orgId}</if>
            <if test="orderCode != null  and orderCode != ''">and ORDER_CODE like concat('%', #{orderCode}, '%')</if>
            <if test="orgName != null  and orgName != ''">and ORG_NAME = #{orgName}</if>
            <if test="orderStatus != null ">and ORDER_STATUS = #{orderStatus}</if>
            <if test="authStartTime != null  and authStartTime != ''">and AUTH_START_TIME = #{authStartTime}</if>
            <if test="authEndTime != null  and authEndTime != ''">and AUTH_END_TIME = #{authEndTime}</if>
            <if test="orderType != null ">and ORDER_TYPE = #{orderType}</if>
            <if test="searchTime!=null and searchTime.length >0">
                and DATE_FORMAT(AUTH_START_TIME,'%Y-%m-%d') &gt;=#{startTime}
                and DATE_FORMAT(AUTH_END_TIME,'%Y-%m-%d') &lt;=#{endTime}
            </if>
        order by p.CREATE_TIME desc
    </select>

    <select id="selectTProOrderById" parameterType="String" resultMap="TProOrderResult">


        select p.ID,p.ORDER_CREATENAME, p.ORDER_CODE, p.ORG_ID, p.KT_DATABASE, p.ORDER_STATUS, p.PRICE, p.DISCOUNT,
        p.FINAL_PRICE, p.AUTH_METHOD, p.AUTH_START_TIME,p. AUTH_END_TIME, ORDER_DESC, p.CREATEBY_ID, p.CREATEBY_NAME,
        p.CREATE_TIME, p.UPDATEBY_ID, p.UPDATEBY_NAME, p.UPDATE_TIME, p.ORDER_TYPE
        ,o.ORG_NAME
        from t_pro_order p
        left join t_plat_organ o
        on p.ORG_ID = o.ID
        where
        p.DEL_FLAG !=1
         and p.ID = #{id}
    </select>

    <insert id="insertTProOrder" parameterType="TProOrder">
        insert into t_pro_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="orderCreateName != null and orderCreateName != ''">ORDER_CREATENAME,</if>
            <if test="orderCode != null and orderCode != ''">ORDER_CODE,</if>
            <if test="orgId != null and orgId != ''">ORG_ID,</if>
            <if test="ktDatabase != null">KT_DATABASE,</if>
            <if test="orderStatus != null">ORDER_STATUS,</if>
            <if test="price != null">PRICE,</if>
            <if test="discount != null">DISCOUNT,</if>
            <if test="finalPrice != null">FINAL_PRICE,</if>
            <if test="authMethod != null">AUTH_METHOD,</if>
            <if test="authStartTime != null">AUTH_START_TIME,</if>
            <if test="authEndTime != null">AUTH_END_TIME,</if>
            <if test="orderDesc != null">ORDER_DESC,</if>
            <if test="createbyId != null">CREATEBY_ID,</if>
            <if test="createbyName != null">CREATEBY_NAME,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updatebyId != null">UPDATEBY_ID,</if>
            <if test="updatebyName != null">UPDATEBY_NAME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="delFlag != null">DEL_FLAG,</if>
            <if test="orderType != null">ORDER_TYPE,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderCreateName != null and orderCreateName != ''">#{orderCreateName},</if>
            <if test="orderCode != null and orderCode != ''">#{orderCode},</if>
            <if test="orgId != null and orgId != ''">#{orgId},</if>
            <if test="ktDatabase != null">#{ktDatabase},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="price != null">#{price},</if>
            <if test="discount != null">#{discount},</if>
            <if test="finalPrice != null">#{finalPrice},</if>
            <if test="authMethod != null">#{authMethod},</if>
            <if test="authStartTime != null">#{authStartTime},</if>
            <if test="authEndTime != null">#{authEndTime},</if>
            <if test="orderDesc != null">#{orderDesc},</if>
            <if test="createbyId != null">#{createbyId},</if>
            <if test="createbyName != null">#{createbyName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatebyId != null">#{updatebyId},</if>
            <if test="updatebyName != null">#{updatebyName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="orderType != null">#{orderType},</if>
        </trim>
    </insert>

    <update id="updateTProOrder" parameterType="TProOrder">
        update t_pro_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderCreateName != null and orderCreateName != ''">ORDER_CREATENAME=#{orderCreateName},</if>
            <if test="orderCode != null and orderCode != ''">ORDER_CODE = #{orderCode},</if>
            <if test="orgId != null and orgId != ''">ORG_ID = #{orgId},</if>
            <if test="ktDatabase != null">KT_DATABASE = #{ktDatabase},</if>
            <if test="orderStatus != null">ORDER_STATUS = #{orderStatus},</if>
            <if test="price != null">PRICE = #{price},</if>
            <if test="discount != null">DISCOUNT = #{discount},</if>
            <if test="finalPrice != null">FINAL_PRICE = #{finalPrice},</if>
            <if test="authMethod != null">AUTH_METHOD = #{authMethod},</if>
            <if test="authStartTime != null">AUTH_START_TIME = #{authStartTime},</if>
            <if test="authEndTime != null">AUTH_END_TIME = #{authEndTime},</if>
            <if test="orderDesc != null">ORDER_DESC = #{orderDesc},</if>
            <if test="createbyId != null">CREATEBY_ID = #{createbyId},</if>
            <if test="createbyName != null">CREATEBY_NAME = #{createbyName},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updatebyId != null">UPDATEBY_ID = #{updatebyId},</if>
            <if test="updatebyName != null">UPDATEBY_NAME = #{updatebyName},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
            <if test="delFlag != null">DEL_FLAG = #{delFlag},</if>
            <if test="orderType != null">ORDER_TYPE = #{orderType},</if>
        </trim>
        where ID = #{id}
    </update>

    <update id="deleteTProOrderById" parameterType="String">
        update  t_pro_order set DEL_FLAG=1 where ID = #{id}
    </update>
<select id="selectByName"  resultType="TProOrder">
    select  AUTH_START_TIME authStartTime,AUTH_END_TIME authEndTime, ORG_ID orgId from t_pro_order where ORG_ID=#{orgId}
</select>
    <delete id="deleteTProOrderByIds" parameterType="String">

        update  t_pro_order set DEL_FLAG=1 where ID in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>



    <select id="selectALl" resultMap="TProOrderResult">
        select DEL_FLAG, ORG_ID,AUTH_START_TIME,AUTH_END_TIME,KT_DATABASE,ID from t_pro_order
    </select>
<update id="updateStaus" parameterType="String">
    update t_pro_order set ORDER_STATUS= 1 where ID=#{id}
</update>

    <update id="updateStaus1"  parameterType="String">
    update t_pro_order set ORDER_STATUS= 2 where ID=#{id}
</update>
    <select id="selectMax" parameterType="String"  resultMap="TProOrderResult">
        select ORDER_CODE from t_pro_order where Date(CREATE_TIME)=#{nowTime}
        order by SUBSTRING(ORDER_CODE, 0, -3) +0 DESC LIMIT 1
    </select>

    <select id="selectOrderByDbname" parameterType="TProOrder"  resultMap="TProOrderResult">
        SELECT
          ORDER_CODE,
          ORDER_STATUS,
          AUTH_METHOD,
          AUTH_END_TIME
        FROM
	      t_pro_order
        WHERE
          ORG_ID = #{orgId}
        AND
          find_in_set(#{ktDatabase}, KT_DATABASE)
    </select>
</mapper>