<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.guliandigital.nameAssociation.mapper.TProNameAssociationMapper">
    <cache type="cn.guliandigital.common.core.redis.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="3600000" />
        <property name="size" value="1024" />
        <property name="readOnly" value="false" />
    </cache>
    <resultMap type="TProNameAssociation" id="TProNameAssociationResult">
        <result property="id"    column="ID"    />
        <result property="pname"    column="PNAME"    />
        <result property="groupId"    column="GROUP_ID"    />
    </resultMap>

    <sql id="selectTProNameAssociationVo">
        select ID, PNAME, GROUP_ID from t_pro_name_association
    </sql>

    <select id="selectTProNameAssociationList" parameterType="TProNameAssociation" resultMap="TProNameAssociationResult">
        <include refid="selectTProNameAssociationVo"/>
        <where>  
            <if test="pname != null  and pname != ''"> and PNAME = #{pname}</if>
            <if test="groupId != null "> and GROUP_ID = #{groupId}</if>
        </where>
    </select>
    
    <select id="selectGroupList" parameterType="TProNameAssociation" resultMap="TProNameAssociationResult">
        select GROUP_ID from t_pro_name_association
        <where>  
            <if test="pname != null  and pname != ''"> and PNAME = #{pname}</if>
            <if test="groupId != null "> and GROUP_ID = #{groupId}</if>
        </where>
        group by GROUP_ID
    </select>
    
    <select id="selectNamesList" resultMap="TProNameAssociationResult">
        select PNAME from t_pro_name_association
       where 1=1 
       	and GROUP_ID in 
          
	       <foreach item="id" collection="list" open="(" separator="," close=")">
	            #{id}
	        </foreach>
    </select>
    
    <select id="selectTProNameAssociationById" parameterType="String" resultMap="TProNameAssociationResult">
        <include refid="selectTProNameAssociationVo"/>
        where ID = #{id}
    </select>
        
    <insert id="insertTProNameAssociation" parameterType="TProNameAssociation">
        insert into t_pro_name_association
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="pname != null">PNAME,</if>
            <if test="groupId != null">GROUP_ID,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="pname != null">#{pname},</if>
            <if test="groupId != null">#{groupId},</if>
         </trim>
    </insert>

    <update id="updateTProNameAssociation" parameterType="TProNameAssociation">
        update t_pro_name_association
        <trim prefix="SET" suffixOverrides=",">
            <if test="pname != null">PNAME = #{pname},</if>
            <if test="groupId != null">GROUP_ID = #{groupId},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteTProNameAssociationById" parameterType="String">
        delete from t_pro_name_association where ID = #{id}
    </delete>

    <delete id="deleteTProNameAssociationByIds" parameterType="String">
        delete from t_pro_name_association where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>