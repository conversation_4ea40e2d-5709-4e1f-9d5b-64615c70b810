package cn.guliandigital.wxamp.domain;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;

/**
 * 订单详情对象 t_wxamp_order_item
 * 
 * <AUTHOR>
 * @date 2023-11-29
 */
public class TWxampOrderItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 订单ID */
    @Excel(name = "订单ID")
    private String orderId;

    /** 数据库ID */
    @Excel(name = "数据库ID")
    private String databaseId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String userId;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal price;

    /** 数量 */
    @Excel(name = "数量")
    private Long quantity;

    /** 授权生效开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "授权生效开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validStartTime;

    /** 授权生效截至日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "授权生效截至日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validExpireTime;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createbyId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createbyName;

    /** 更新人ID */
    @Excel(name = "更新人ID")
    private String updatebyId;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private Integer delFlag;


    //修改状态
    private String orderValid;

    public String getOrderValid() {
        return orderValid;
    }

    public void setOrderValid(String orderValid) {
        this.orderValid = orderValid;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setDatabaseId(String databaseId) 
    {
        this.databaseId = databaseId;
    }

    public String getDatabaseId() 
    {
        return databaseId;
    }
    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }
    public void setPrice(BigDecimal price) 
    {
        this.price = price;
    }

    public BigDecimal getPrice() 
    {
        return price;
    }
    public void setQuantity(Long quantity) 
    {
        this.quantity = quantity;
    }

    public Long getQuantity() 
    {
        return quantity;
    }
    public void setValidStartTime(Date validStartTime) 
    {
        this.validStartTime = validStartTime;
    }

    public Date getValidStartTime() 
    {
        return validStartTime;
    }
    public void setValidExpireTime(Date validExpireTime) 
    {
        this.validExpireTime = validExpireTime;
    }

    public Date getValidExpireTime() 
    {
        return validExpireTime;
    }
    public void setCreatebyId(String createbyId) 
    {
        this.createbyId = createbyId;
    }

    public String getCreatebyId() 
    {
        return createbyId;
    }
    public void setCreatebyName(String createbyName) 
    {
        this.createbyName = createbyName;
    }

    public String getCreatebyName() 
    {
        return createbyName;
    }
    public void setUpdatebyId(String updatebyId) 
    {
        this.updatebyId = updatebyId;
    }

    public String getUpdatebyId() 
    {
        return updatebyId;
    }
    public void setUpdatebyName(String updatebyName) 
    {
        this.updatebyName = updatebyName;
    }

    public String getUpdatebyName() 
    {
        return updatebyName;
    }
    public void setDelFlag(Integer delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("databaseId", getDatabaseId())
            .append("userId", getUserId())
            .append("price", getPrice())
            .append("quantity", getQuantity())
            .append("validStartTime", getValidStartTime())
            .append("validExpireTime", getValidExpireTime())
            .append("createbyId", getCreatebyId())
            .append("createbyName", getCreatebyName())
            .append("createTime", getCreateTime())
            .append("updatebyId", getUpdatebyId())
            .append("updatebyName", getUpdatebyName())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
