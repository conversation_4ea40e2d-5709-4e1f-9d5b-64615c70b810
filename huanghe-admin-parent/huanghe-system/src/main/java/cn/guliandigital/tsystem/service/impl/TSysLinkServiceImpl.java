package cn.guliandigital.tsystem.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.core.domain.model.DisplayParam;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.tsystem.domain.TSysLink;
import cn.guliandigital.tsystem.mapper.TSysLinkMapper;
import cn.guliandigital.tsystem.service.ITSysLinkService;

/**
 * 友情链接Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-09-11
 */
@Service
public class TSysLinkServiceImpl implements ITSysLinkService 
{
    @Autowired
    private TSysLinkMapper tSysLinkMapper;

    /**
     * 查询友情链接
     * 
     * @param id 友情链接ID
     * @return 友情链接
     */
    @Override
    public TSysLink selectTSysLinkById(String id)
    {
        return tSysLinkMapper.selectTSysLinkById(id);
    }

    /**
     * 查询友情链接列表
     * 
     * @param tSysLink 友情链接
     * @return 友情链接
     */
    @Override
    public List<TSysLink> selectTSysLinkList(TSysLink tSysLink)
    {
//        List<TSysLink> list = new ArrayList<>();
//        TSysLink tSysLink1 = new TSysLink(IdUtils.simpleUUID(),"历代诗咏数据库","",1,"","","","",0,"http://114.255.120.44/huanghe-mobile/common/download/resource?name=/profile/upload/2023/wxamp/3.png","","");
//        TSysLink tSysLink2 = new TSysLink(IdUtils.simpleUUID(),"齐鲁文献资料库","",1,"","","","",0,"http://114.255.120.44/huanghe-mobile/common/download/resource?name=/profile/upload/2023/wxamp/2.png","","");
//        TSysLink tSysLink3 = new TSysLink(IdUtils.simpleUUID(),"中华武术知识库","",1,"","","","",0,"http://114.255.120.44/huanghe-mobile/common/download/resource?name=/profile/upload/2023/wxamp/1.png","","");
//        TSysLink tSysLink4 = new TSysLink(IdUtils.simpleUUID(),"历代诗咏数据库","",1,"","","","",0,"http://114.255.120.44/huanghe-mobile/common/download/resource?name=/profile/upload/2023/wxamp/3.png","","");
//        TSysLink tSysLink5 = new TSysLink(IdUtils.simpleUUID(),"齐鲁文献资料库","",1,"","","","",0,"http://114.255.120.44/huanghe-mobile/common/download/resource?name=/profile/upload/2023/wxamp/2.png","","");
//        TSysLink tSysLink6 = new TSysLink(IdUtils.simpleUUID(),"中华武术知识库","",1,"","","","",0,"http://114.255.120.44/huanghe-mobile/common/download/resource?name=/profile/upload/2023/wxamp/1.png","","");
//        list.add(tSysLink1);
//        list.add(tSysLink2);
//        list.add(tSysLink3);
//        list.add(tSysLink4);
//        list.add(tSysLink5);
//        list.add(tSysLink6);
        List<TSysLink> list = tSysLinkMapper.selectTSysLinkList(tSysLink);

        return list;
    }

    /**
     * 新增友情链接
     * 
     * @param tSysLink 友情链接
     * @return 结果
     */
    @Override
    public int insertTSysLink(TSysLink tSysLink)
    {
        tSysLink.setId(IdUtils.simpleUUID());
        tSysLink.setCreateTime(DateUtil.getCuurentDate());
        tSysLink.setCreatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysLink.setCreatebyName(SecurityUtils.getUsername());
        //新增加的数据排序编号为当前最大的
        int webDisplay = tSysLinkMapper.selectTSysLinkDisplayMax() + 1;
        tSysLink.setWebDisplay(webDisplay);
        return tSysLinkMapper.insertTSysLink(tSysLink);
    }

    /**
     * 修改友情链接
     * 
     * @param tSysLink 友情链接
     * @return 结果
     */
    @Override
    public int updateTSysLink(TSysLink tSysLink)
    {
        tSysLink.setUpdateTime(DateUtil.getCuurentDate());
        tSysLink.setUpdatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tSysLink.setUpdatebyName(SecurityUtils.getUsername());
        return tSysLinkMapper.updateTSysLink(tSysLink);
    }

    /**
     * 批量删除友情链接
     * 
     * @param ids 需要删除的友情链接ID
     * @return 结果
     */
    @Override
    public int deleteTSysLinkByIds(String[] ids)
    {
        return tSysLinkMapper.deleteTSysLinkByIds(ids);
    }

    /**
     * 删除友情链接信息
     * 
     * @param id 友情链接ID
     * @return 结果
     */
    @Override
    public int deleteTSysLinkById(String id)
    {
        return tSysLinkMapper.deleteTSysLinkById(id);
    }

    /**
     * 修改友情链接排序
     *
     * @param displayParam 友情链接排序对象
     * @return 结果
     */
    @Override
    public int updateWebDisplay(DisplayParam displayParam) {
        String[] display = displayParam.getDisplay().split(":");
        String displayId = display[0];
        String displayNum = display[1];
        String[] displayAfter = displayParam.getDisplayAfter().split(":");
        String displayAfterId = displayAfter[0];
        String displayAfterNum = displayAfter[1];
        TSysLink tSysLink = new TSysLink();
        tSysLink.setId(displayId);
        tSysLink.setWebDisplay(Integer.valueOf(displayAfterNum));
        tSysLinkMapper.updateTSysLink(tSysLink);

        tSysLink = new TSysLink();
        tSysLink.setId(displayAfterId);
        tSysLink.setWebDisplay(Integer.valueOf(displayNum));
        return tSysLinkMapper.updateTSysLink(tSysLink);
    }
}
