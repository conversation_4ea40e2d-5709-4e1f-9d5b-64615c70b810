package cn.guliandigital.analysis.asyn;


import cn.guliandigital.analysis.service.IAutoPDFSynService;
import cn.guliandigital.analysis.service.IAutoSynbookService;
import cn.guliandigital.common.enums.DataStatusEnum;
import cn.guliandigital.common.enums.InStatusEnum;
import cn.guliandigital.storage.storage.domain.TTaskSettle;
import cn.guliandigital.storage.storage.service.ITTaskSettleService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import java.io.File;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SynBooksConsumer {

	@Autowired
	private ITTaskSettleService taskSettleService;

	@Autowired
	private IAutoPDFSynService autoPDFSynService;

	@Autowired
	private IAutoSynbookService synbookService;


	@Async(value = "handleTaskExecutor")
	public void listenQueue(String json) {
		log.info("==>接收的推送数据是：{}", json);
		TTaskSettle task = JSONObject.parseObject(json, TTaskSettle.class);
		task.setErrorCount(new AtomicInteger());
		task.setAllCount(new AtomicInteger());
		String dataPath = task.getDataPath();
		File file=new File(dataPath);
		File[] files = file.listFiles();
		assert files != null;
		for (File f:files){
			if (f.isDirectory()){
				if ("全文".equals(f.getName())){
					//解析全文数据
					synbookService.handleBooks(task,f);
				}else if ("pdf".equalsIgnoreCase(f.getName())){
					//解析pdf
					autoPDFSynService.handlePDFBook(task,f);
				}
			}
		}
		int all = task.getAllCount().get();
		int error = task.getErrorCount().get();
		if (error==all){
			//解析失败
			task.setDataStatus(DataStatusEnum.LOST.getCode());
			task.setInStatus(InStatusEnum.LOST.getCode());
		}else {
			if (error==0){
				//解析成功
				task.setDataStatus(DataStatusEnum.SUCCESS.getCode());
				task.setInStatus(InStatusEnum.SUCCESS.getCode());
			}else {
				//部分成功
				task.setDataStatus(DataStatusEnum.WARNING.getCode());
				task.setInStatus(InStatusEnum.SUCCESS.getCode());
			}
		}
		if (all==0){
			task.setDataRemark("未找到可以解析入库的资源文件");
		}else {
			task.setDataRemark("一共需要解析"+all+"本资源，有"+error+"本书解析失败，具体查看日志");
		}
		task.setParseEndTime(new Date());
		taskSettleService.updateTTaskSettle(task);
	}
}
