package cn.guliandigital.product.book.service;

import cn.guliandigital.product.book.domain.TBookFileMongo;
import java.util.List;
import java.util.Map;

public interface TBookFileMongoService {



    TBookFileMongo findByCondition(Map<String, Object> condition);
	
	
	/**
     * 添加
     *
     * @param
     */
    void add(TBookFileMongo t);

    /**
     * 根据id删除
     *
     * @param id
     * @return 
     */
    void deleteById(String id);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    TBookFileMongo findById(String id);

    /**
     * 更新
     *
     * @param role
     */
    void updateById(TBookFileMongo t);

	/**
	 * 通过条件查询集合
	 * @param condition
	 * @return
	 */
    List<TBookFileMongo> findListByCondition(Map<String, Object> condition);

    
    int deleteByCondition(String bookId);
}