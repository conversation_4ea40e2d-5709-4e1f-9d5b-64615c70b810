package cn.guliandigital.product.book.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;


/**
 * 章节内容对应表
 * <AUTHOR>
 *
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Document(collection = "t_huabghe_book_file")
public class TBookFileMongo {

	/**
	 * 资源ID
	 */
	@Id
	private String id;

	/**
	 * 图书ID
	 */
	private String bookId;
	
	/**
	 * 书名
	 */
	private String bookName;

	//文件名称
	private String fileName;

	//文件路径
	private String filePath;

	//文件后缀
	private String extension;

	
}
