package cn.guliandigital.product.database.service.impl;


import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.guliandigital.common.enums.DbStatus;
import cn.guliandigital.common.exception.ServiceException;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.SecurityUtils;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.book.domain.TProBooks;
import cn.guliandigital.product.book.mapper.TProBooksMapper;
import cn.guliandigital.product.database.domain.TProDatabase;
import cn.guliandigital.product.database.mapper.TProDatabaseMapper;
import cn.guliandigital.product.database.service.ITProDatabaseService;

/**
 * 数据库管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-09-14
 */
@Service
public class TProDatabaseServiceImpl implements ITProDatabaseService {
    @Autowired
    private TProDatabaseMapper tProDatabaseMapper;
    @Autowired
    private TProBooksMapper tProBooksMapper;

    /**
     * 查询数据库管理
     *
     * @param id 数据库管理ID
     * @return 数据库管理
     */
    @Override
    public TProDatabase selectTProDatabaseById(String id) {
        return tProDatabaseMapper.selectTProDatabaseById(id);
    }

    /**
     * 查询数据库管理列表
     *
     * @param tProDatabase 数据库管理
     * @return 数据库管理
     */
    @Override
    public List<TProDatabase> selectTProDatabaseList(TProDatabase tProDatabase) {
        return tProDatabaseMapper.selectTProDatabaseList(tProDatabase);
    }

    /**
     * 新增数据库管理
     *
     * @param tProDatabase 数据库管理
     * @return 结果
     */
    @Override
    public int insertTProDatabase(TProDatabase tProDatabase) {
        if(Objects.isNull(tProDatabase.getDbStatus())){
            tProDatabase.setDbStatus("0");
        }
        tProDatabase.setCreatebyId(SecurityUtils.getLoginUser().getUser().getUserId().toString());
        tProDatabase.setCreatebyName(SecurityUtils.getUsername());
        tProDatabase.setCreateTime(DateUtil.getCuurentDate());
        tProDatabase.setDbId(IdUtils.simpleUUID());
        tProDatabase.setBookCount(0);
        tProDatabase.setDelFlag(0);
        return tProDatabaseMapper.insertTProDatabase(tProDatabase);
    }

    /**
     * 修改数据库管理
     *
     * @param tProDatabase 数据库管理
     * @return 结果
     */
    @Override
    public int updateTProDatabase(TProDatabase tProDatabase) {
        if(tProDatabase.getDbStatus().equals(DbStatus.DOWN.getCode())){
            List<TProBooks> tProBooks = tProBooksMapper.selectTProBooksList(new TProBooks(){{setDbId(tProDatabase.getDbId());}});
            if(tProBooks.size()>0){
                throw  new ServiceException("当前数据库中存在图书资源，无法下架！");
            }
        }
        tProDatabase.setUpdateTime(DateUtil.getCuurentDate());
        return tProDatabaseMapper.updateTProDatabase(tProDatabase);
    }

    /**
     * 批量删除数据库管理
     *
     * @param ids 需要删除的数据库管理ID
     * @return 结果
     */
    @Override
    public int deleteTProDatabaseByIds(String[] ids) {
       for (String id : ids) {
            TProDatabase tProDatabase = tProDatabaseMapper.selectTProDatabaseById(id);

            if(tProDatabase.getDbStatus().equals(DbStatus.UP.getCode())){
                throw  new ServiceException("当前数据库未下架，不能删除！");
            }
        }
        return tProDatabaseMapper.deleteTProDatabaseByIds(ids);
    }

    /**
     * 删除数据库管理信息
     *
     * @param id 数据库管理ID
     * @return 结果
     */
    @Override
    public int deleteTProDatabaseById(String id) {
        return tProDatabaseMapper.deleteTProDatabaseById(id);
    }

    @Override
    public List<TProDatabase> selectRe() {
        return tProDatabaseMapper.selectRe();
    }

	@Override
	public TProDatabase selecDbId(String dbName) {		
		return tProDatabaseMapper.selecDbId(dbName);
	}

    @Override
    public List<TProDatabase> selectTProDatabase(TProDatabase tProDatabase) {
        return tProDatabaseMapper.selectTProDatabase(tProDatabase);
    }
}
