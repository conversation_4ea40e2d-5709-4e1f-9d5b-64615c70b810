package cn.guliandigital.product.readhistory.service;

import java.util.List;

import cn.guliandigital.product.readhistory.domain.TUserReadBook;

/**
 * 用户阅读进度Service接口
 * 
 * <AUTHOR>
 * @date 2022-02-10
 */
public interface ITUserReadBookService 
{
    /**
     * 查询用户阅读进度
     * 
     * @param id 用户阅读进度ID
     * @return 用户阅读进度
     */
        TUserReadBook selectTUserReadBookById(String id);

    /**
     * 查询用户阅读进度列表
     * 
     * @param tUserReadBook 用户阅读进度
     * @return 用户阅读进度集合
     */
    List<TUserReadBook> selectTUserReadBookList(TUserReadBook tUserReadBook);
    /**
     * 查询用户阅读进度列表 (小程序专用)
     * @param tUserReadBook
     * @return
     */
    List<TUserReadBook> selectTUserReadBookListOrderBy(TUserReadBook tUserReadBook);

    /**
     * 新增用户阅读进度
     * 
     * @param tUserReadBook 用户阅读进度
     * @return 结果
     */
    int insertTUserReadBook(TUserReadBook tUserReadBook);

    /**
     * 修改用户阅读进度
     * 
     * @param tUserReadBook 用户阅读进度
     * @return 结果
     */
    int updateTUserReadBook(TUserReadBook tUserReadBook);

    /**
     * 批量删除用户阅读进度
     * 
     * @param ids 需要删除的用户阅读进度ID
     * @return 结果
     */
    int deleteTUserReadBookByIds(String[] ids);

    /**
     * 删除用户阅读进度信息
     * 
     * @param id 用户阅读进度ID
     * @return 结果
     */
    int deleteTUserReadBookById(String id);
}
