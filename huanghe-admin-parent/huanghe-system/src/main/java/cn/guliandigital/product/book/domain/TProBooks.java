package cn.guliandigital.product.book.domain;

import java.io.File;
import java.util.List;

import cn.guliandigital.common.utils.pdf.PdfContent;
import cn.guliandigital.common.utils.pdf.PdfUtil;
import com.fasterxml.jackson.annotation.JsonInclude;

import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;
import cn.guliandigital.storage.storage.domain.XmlBean;
import lombok.Data;

/**
 * 产品资源管理对象 t_pro_books
 *
 * <AUTHOR>
 * @date 2020-09-09
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TProBooks extends BaseEntity {
    private static final long serialVersionUID = 1L;
    private Integer dbCount;
    private String orgId;
    /**
     * $column.columnComment
     */
    private String id;
    /**
     * 唯一标识符
     */
    private String uniqueId;
    /**
     * 书名
     */
    @Excel(name = "书名")
    private String bookName;
    /**
     * 资源分类
     */
    @Excel(name = "资源分类")
    private String resourceClasses;

    private String resourceClassesId;

    private String[] resourceClassesIds;

    //阅读时长
    private String readTime;
    //浏览人数
    private String searchPeople;
    /**
     */
    @Excel(name = "资源类型")
    private String resourceType;
    /**
     * 四部分类法
     */
    public String siClassificationId;
    @Excel(name = "四部分类法")
    private String siClassification;
    /**
     * 中图分类法编号
     */
    private String classiMethodCode;
    private String readMode;
    /**
     * 中图分类法
     */
    private String classiMethod;
    /**
     * 其他分类
     */
    private String classification;
    /**
     * 主要责任者
     */
    @Excel(name = "主要责任者")
    private String mainResponsibility;
    /**
     * 出版日期
     */
    @Excel(name = "出版日期")
    private String publishDate;
    /**
     * 出版者
     */
    @Excel(name = "出版社")
    private String publisher;
    /**
     * 出版地
     */
    private String publishland;
    /**
     * 出版年份
     */
    @Excel(name = "出版年份")
    private String publishYear;
    /**
     * 状态 0-下架 1-上架
     */
    @Excel(name = "状态 0-下架 1-上架")
    private Integer proStatus;
    /**
     * 0-开放  1-收费
     */
    private Integer openSource;
    /**
     * 产品价格
     */
    private String price;
    @Excel(name = "丛编")
    private String dbName;
    
    //图文类型 P-图片  T-图文 SR-版式还原
    private String imageTextType; 
    /**
     * <AUTHOR>
     * @Description
     * @Date 2020/9/18 17:15
     * @param null:主题词
     */
    @Excel(name = "主题词-词频")
    private String subjectWord;

    @Excel(name = "主题词-内容")
    private String subjectText;

    /**
     * <AUTHOR>
     * @Description 版次
     * @Date 2020/9/18 17:16
     * @param null:
     */
    @Excel(name = "版次")
    private String revision;
    /**
     * 图片路径
     */
    private String coverUrl;
    /**
     * 缩微图
     */
    private String thumbCoverUrl;
    
    private String coverPath;
    /**
     * 图书描述
     */
    @Excel(name = "图书描述")
    private String bookDesc;
    /**
     * $column.columnComment
     */
    private String createbyId;
    /**
     * 创建人
     */
    private String createbyName;
    /**
     * $column.columnComment
     */
    private String updatebyId;
    /**
     * 更新人
     */
    private String updatebyName;
    /**
     * 删除标识
     */
    private Integer delFlag;
    private String dbId;
    /**
     * 文件路径-列表
     */
    private List<String> xmlFileList;
    /**
     * 标题内容-列表
     */
    private List<XmlBean> xmlMenuContentList;
    //是否加入书架
    private Boolean haveAddshelf;
    //书架ID
    private String shelfClassId;
    private Integer display;
    private String numberCopies;
    private String conglomeration;

    private Integer level;

    /** 图片数 */
    private Integer picNum;

    /** 字数（千字） */
    private Long wordNum;

    private Long resourceSize;

    /** 版本 */
    private String edition;

    private Boolean isBuy;
    private String dcMetaid;


    private String collection;//馆藏
    /** 印次 */
    private String impression;
    /** 开本信息 */
    private String kaibenInfo;

    /**
     *  解析状态  解析中-running   成功-success  失败-lost
     */
    private String parseStatus;
    /**
     * 是否改变字号  true:是 false:否
     */
    private Boolean changeFontsize;
    /**
     * ocr版本 2代表旧版ocr 3代表新版ocr
     */
    private String ocrVersion;
    /**
     * 排版，默认无值为图片文字竖版显示，横版-图片文字为横版显示
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String layout;

    
    private String pdfPath;//pdf文件路径

    private String pdfName;//pdf名称

    private String coverName;//封面图片名称

    private List<PdfContent> cntentList;

    private List<PdfContent> menuList;

    private List<File> XmlFiles;
    
    
    @SuppressWarnings("all")
    protected boolean canEqual(final Object other) {
        return other instanceof TProBooks;
    }
}
