package cn.guliandigital.product.clasic.service.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import cn.guliandigital.es.domain.Navigation;
import cn.guliandigital.product.book.vo.ClassTreeType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.houbb.opencc4j.util.ZhConverterUtil;

import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.uuid.IdUtils;
import cn.guliandigital.product.clasic.domain.TConfigClassicTree;
import cn.guliandigital.product.clasic.mapper.TConfigClassicTreeMapper;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;
import lombok.extern.slf4j.Slf4j;

/**
 * 分类树Service业务层处理
 * 
 * <AUTHOR>
 * @date 2020-09-07
 */

@Slf4j
@Service
public class TConfigClassicTreeServiceImpl implements ITConfigClassicTreeService {
	@Autowired
	private TConfigClassicTreeMapper tConfigClassicTreeMapper;

	/**
	 * 查询分类树
	 * 
	 * @param id
	 *            分类树ID
	 * @return 分类树
	 */
	@Override
	public List<TConfigClassicTree> selectTConfigClassicTreeById(String id) {
		return tConfigClassicTreeMapper.selectTConfigClassicTreeById(id);
	}

	@Override
	public List<TConfigClassicTree> selectClassicTreeById(String id) {
		return tConfigClassicTreeMapper.selectClassicTreeById(id);
	}

	/**
	 * 查询分类树列表
	 * 
	 * @param tConfigClassicTree
	 *            分类树
	 * @return 分类树
	 */
	@Override
	public List<TConfigClassicTree> selectTConfigClassicTreeList(TConfigClassicTree tConfigClassicTree) {
		return tConfigClassicTreeMapper.selectTConfigClassicTreeList(tConfigClassicTree);
	}


	/**
	 * 新增分类树
	 * 
	 * @param tConfigClassicTree
	 *            分类树
	 * @return 结果
	 */
	@Override
	public int insertTConfigClassicTree(TConfigClassicTree tConfigClassicTree) {
//		TConfigClassicTree CattConfigClassicTree =	tConfigClassicTreeMapper.selectByName(tConfigClassicTree.getTreeName());
//		if(!Objects.isNull(CattConfigClassicTree)){
//			throw new ServiceException("已有改分类！");
//		}

		tConfigClassicTree.setTreeNameS(tConfigClassicTree.getTreeName());
		tConfigClassicTree.setTreeName(ZhConverterUtil.convertToSimple(tConfigClassicTree.getTreeName()));
		tConfigClassicTree.setCreateTime(DateUtil.getCuurentDate());
		tConfigClassicTree.setId(IdUtils.simpleUUID());
		tConfigClassicTree.setTreePid(tConfigClassicTree.getTreePid());
		tConfigClassicTree.setTreeCode(tConfigClassicTree.getTreeCode());
		if (tConfigClassicTree.getTreePid() == null) {
			tConfigClassicTree.setTreePid(null);
		}

		tConfigClassicTree.setClassicId(tConfigClassicTree.getClassicId());

		return tConfigClassicTreeMapper.insertTConfigClassicTree(tConfigClassicTree);
	}

	/**
	 * 修改分类树
	 * 
	 * @param tConfigClassicTree
	 *            分类树
	 * @return 结果
	 */
	@Override
	public int updateTConfigClassicTree(TConfigClassicTree tConfigClassicTree) {
		tConfigClassicTree.setTreeNameS(tConfigClassicTree.getTreeName());
		tConfigClassicTree.setTreeName(ZhConverterUtil.convertToSimple(tConfigClassicTree.getTreeName()));
		return tConfigClassicTreeMapper.updateTConfigClassicTree(tConfigClassicTree);
	}

	/**
	 * 批量删除分类树
	 * 
	 * @param
	 */
	@Override
	public void deleteTConfigClassicTreeByIds(String id) {
		
		List<TConfigClassicTree> tConfigClassicTree = tConfigClassicTreeMapper.selectHasChild(id);
		for (TConfigClassicTree configClassicTree : tConfigClassicTree) {
			if(StringUtil.isNotEmpty(configClassicTree.getId())) {
				log.info("==>删除子节点：{}",configClassicTree.getId());
				tConfigClassicTreeMapper.deleteTConfigClassicTreeById(configClassicTree.getId());
			}
		}
		tConfigClassicTreeMapper.deleteTConfigClassicTreeById(id);
	}

	@Override
	public List<TConfigClassicTree> selectParent(String treePid) {
		return tConfigClassicTreeMapper.selectHasChild(treePid);
	}

	@Override
	public TConfigClassicTree selectP(String treePid) {
		return tConfigClassicTreeMapper.selectP(treePid);
	}


	@Override
	public List<Navigation> getSearchTree(List<Navigation> navigationList) {
		List<Navigation> list=new ArrayList<>();
		for (Navigation navigation : navigationList) {
			String classesId = navigation.getId(); // 聚合字段列的值
			long docCount = navigation.getCount();// 聚合字段对应的数量
			ClassTreeType _tree = tConfigClassicTreeMapper.selectById(classesId);
			if(_tree != null) {
				Navigation n=new Navigation();
				n.setId(_tree.getId());
				n.setName(_tree.getLabel());
				n.setPid(_tree.getTreePid());
				n.setCount(docCount);
				n.setDisplay(_tree.getDisplay());
				List<Navigation> collect = list.stream().filter(f -> StringUtil.equals(f.getId(), n.getId())).collect(Collectors.toList());
				if (StringUtil.isNotEmpty(collect)){
					list.removeAll(collect);
				}
				list.add(n);
				//循环查找父级节点
				recursionNavigation(n,list);
			}
		}
		//组装树型结构
		List<Navigation> treeList = buildNavigationTree(list);
		for (Navigation navigation:treeList){
			AtomicLong atomic=new AtomicLong();
			atomic.addAndGet(navigation.getCount());
			List<Navigation> children = navigation.getChildren();
			if (StringUtil.isNotEmpty(children)){
				for (Navigation n:children){
					atomic.addAndGet(n.getCount());
				}
			}
			navigation.setCount(atomic.get());
		}
		return treeList;
	}

	/**
	 * 递归组装左侧导航 子级找父级
	 */
	private void recursionNavigation(Navigation navigation,List<Navigation> list){
		//查询分类
		if (StringUtil.isNotEmpty(navigation.getPid())){
			//查询父节点
			ClassTreeType _tree = tConfigClassicTreeMapper.selectById(navigation.getPid());
			Navigation cn=new Navigation(_tree.getLabel(),0L,_tree.getId(),
					_tree.getTreePid(),_tree.getDisplay());
			long count = list.stream().filter(f -> StringUtil.equals(f.getId(), cn.getId())).count();
			if (count==0){
				list.add(cn);
			}
			//继续寻找子节点
			recursionNavigation(cn,list);
		}
	}

	private List<Navigation> buildNavigationTree(List<Navigation> navigationList){
		//子节点按照父节点分组
		Map<String, List<Navigation>> collect = navigationList.stream().filter(f -> StringUtil.isNotEmpty(f.getPid())).
				collect(Collectors.groupingBy(Navigation::getPid));
		//获取子节点
		navigationList.forEach(t->{
			List<Navigation> children = collect.get(t.getId());
			if (StringUtil.isNotEmpty(children)){
				children.sort(Comparator.comparing(Navigation::getDisplay));
				t.setChildren(children);
				if (StringUtil.isNotEmpty(t.getPid())){
					//计算数量
					AtomicLong atomic=new AtomicLong(t.getCount());
					children.forEach(c->{
						atomic.addAndGet(c.getCount());
					});
					t.setCount(atomic.get());
				}
			}
		});
		//获取所有父级节点
		return navigationList.stream()
				.filter(f -> StringUtil.isEmpty(f.getPid()))
				.sorted(Comparator.comparing(Navigation::getDisplay))
				.collect(Collectors.toList());
	}

	@Override
	public List<String> getSearchId(String classicId) {
		List<String> list=new ArrayList<>();
		list.add(classicId);
		recursion(classicId,list);
		return list.stream().distinct().collect(Collectors.toList());
	}

	//递归查询 父级查询子级
	private void recursion(String classicId,List<String> list){
		TConfigClassicTree classicTree = new TConfigClassicTree();
		classicTree.setTreePid(classicId);
		List<TConfigClassicTree> treeList =selectTConfigClassicTreeList(classicTree);
		if (StringUtil.isNotEmpty(treeList)){
			for (TConfigClassicTree tree:treeList){
				list.add(tree.getId());
				recursion(tree.getId(),list);
			}
		}
	}

}
