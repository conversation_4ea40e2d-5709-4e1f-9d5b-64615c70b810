package cn.guliandigital.product.book.domain;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;


/**
 * 章节内容对应表
 * <AUTHOR>
 *
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class TBookContentVo {

	/**
	 * 资源ID
	 */
	private String id;

	/**
	 * 图书ID
	 */
	private String bookId;
	
	/**
	 * 章节ID
	 */
	private String menuId;

	/**
	 * 图片ID
	 */
	private String picId;
	
	/**
	 * 密文
	 */
	private String bookContentEnc;
	
    /**
     * 开始下标
     */
	private Integer startIdx;
	
	/**
	 * 关键字
	 */
	private String keys;

	/**
	 * 阅读模式  S-简体  T-繁体  O-不带标点  P-带标点
	 */
	private String readMode;
	
	/**
	 * 笔记内容
	 */
	private String noteWord;	
	
	private Integer pageNo;
	
	private String noteId;
	
	private String jump;
	
	/*
	 * pre next click
	 */
	private String direct;
		
}
