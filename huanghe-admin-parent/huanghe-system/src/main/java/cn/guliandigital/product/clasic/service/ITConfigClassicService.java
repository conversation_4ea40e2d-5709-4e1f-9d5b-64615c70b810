package cn.guliandigital.product.clasic.service;

import cn.guliandigital.product.clasic.domain.TConfigClassic;

import java.util.List;

/**
 * 分类定义Service接口
 * 
 * <AUTHOR>
 * @date 2020-09-07
 */
public interface ITConfigClassicService 
{
    /**
     * 查询分类定义
     * 
     * @param id 分类定义ID
     * @return 分类定义
     */
        TConfigClassic selectTConfigClassicById(String id);

    /**
     * 查询分类定义列表
     * 
     * @param tConfigClassic 分类定义
     * @return 分类定义集合
     */
    List<TConfigClassic> selectTConfigClassicList(TConfigClassic tConfigClassic);

    /**
     * 新增分类定义
     * 
     * @param tConfigClassic 分类定义
     * @return 结果
     */
    int insertTConfigClassic(TConfigClassic tConfigClassic);

    /**
     * 修改分类定义
     * 
     * @param tConfigClassic 分类定义
     * @return 结果
     */
    int updateTConfigClassic(TConfigClassic tConfigClassic);

    /**
     * 批量删除分类定义
     * 
     * @param ids 需要删除的分类定义ID
     * @return 结果
     */
    int deleteTConfigClassicByIds(String[] ids);

    /**
     * 删除分类定义信息
     * 
     * @param id 分类定义ID
     * @return 结果
     */
    int deleteTConfigClassicById(String id);


    int batchDel(List<TConfigClassic> tConfigClassics);

    int changeStatus(TConfigClassic tConfigClassic);

/**
 * <AUTHOR>
 * @Description
 * @Date 2020/9/7
 * @param classicId:查詢詳情
 **/

}
