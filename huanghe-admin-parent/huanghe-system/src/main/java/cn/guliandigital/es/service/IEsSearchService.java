package cn.guliandigital.es.service;

import cn.guliandigital.es.domain.AdvanceSearchVo;
import cn.guliandigital.es.domain.Navigation;
import cn.guliandigital.es.domain.SearchVo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface IEsSearchService {


    /**
     * 库内检索
     * @param searchVo
     * @param pageable
     * @return
     */
    Page<?> findGeneralSearchList(SearchVo searchVo, Pageable pageable);

    /**
     * 高级检索
     * @param searchVo
     * @param pageable
     * @return
     */
    Page<?> findAdvanceSearchList(AdvanceSearchVo searchVo, Pageable pageable);

    /**
     * 快速检索统计
     * @param searchVo
     * @param field
     * @return
     */
    List<Navigation> findAggregationSearch(SearchVo searchVo, String field);

    /**
     * 高级检索统计
     * @param searchVo
     * @param field
     * @return
     */
    List<Navigation> findAggregationSearch(AdvanceSearchVo searchVo, String field);



    /**
     * 快速检索查询总数量
     * @param searchVo
     * @return
     */
    Long querySearchCount(SearchVo searchVo);

    /**
     * 高级检索查询总数量
     * @param searchVo
     * @return
     */
    Long querySearchCount(AdvanceSearchVo searchVo);
}
