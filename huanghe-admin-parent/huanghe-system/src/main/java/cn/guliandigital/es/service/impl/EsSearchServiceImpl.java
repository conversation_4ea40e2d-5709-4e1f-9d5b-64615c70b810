package cn.guliandigital.es.service.impl;

import cn.guliandigital.common.constant.EsConstants;
import cn.guliandigital.common.enums.DelFlagEnum;
import cn.guliandigital.common.utils.DateUtil;
import cn.guliandigital.common.utils.EsUtil;
import cn.guliandigital.common.utils.HighlightUtils;
import cn.guliandigital.common.utils.StringUtil;
import cn.guliandigital.common.utils.word.IkUtils;
import cn.guliandigital.es.domain.*;
import cn.guliandigital.es.service.IEsSearchService;
import cn.guliandigital.product.clasic.service.ITConfigClassicTreeService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder.Field;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.ScoreSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.ResultsExtractor;
import org.springframework.data.elasticsearch.core.SearchResultMapper;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.aggregation.impl.AggregatedPageImpl;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;

@Slf4j
@Service
public class EsSearchServiceImpl implements IEsSearchService {

    @Autowired
    private ElasticsearchTemplate elasticsearchTemplate;

    @Autowired
    private ITConfigClassicTreeService tConfigClassicTreeService;
    
    /**
     * 库内检索
     * @param searchVo
     * @param pageable
     * @return
     */
    @Override
    public Page<?> findGeneralSearchList(SearchVo searchVo, Pageable pageable) {
        try {
            BoolQueryBuilder boolQueryBuilder = buildGeneralSearchQuery(searchVo);
            NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
            //设置排序
            String orderByColumn = searchVo.getOrderByColumn();
            String isAsc = searchVo.getIsAsc();
            FieldSortBuilder fieldSortBuilder=null;
            ScoreSortBuilder scoreSortBuilder = null;
            if(StringUtil.isEmpty(orderByColumn)||orderByColumn.equals("default")) {
                scoreSortBuilder = SortBuilders.scoreSort().order(SortOrder.DESC);
            }else {
                if(StringUtil.isEmpty(isAsc)||StringUtil.equalsIgnoreCase(isAsc, "asc")) {
                    fieldSortBuilder = SortBuilders.fieldSort(orderByColumn).order(SortOrder.ASC);
                }else {
                    fieldSortBuilder = SortBuilders.fieldSort(orderByColumn).order(SortOrder.DESC);
                }
            }
            if (StringUtil.isNotNull(fieldSortBuilder)){
                nativeSearchQueryBuilder.withSort(fieldSortBuilder).withQuery(boolQueryBuilder);
            }else if (StringUtil.isNotNull(scoreSortBuilder)){
                if (searchVo.getResultType()==1){
                    //全文结果
                    List<String> list = EsUtil.scoreSortParams(searchVo.getKeyword());
                    EsUtil.scoreSort(nativeSearchQueryBuilder,boolQueryBuilder,list);
                }else {
                    nativeSearchQueryBuilder.withSort(scoreSortBuilder).withQuery(boolQueryBuilder);
                }
            }
            //设置高亮
            Field[] fields =null;
            String[] fieldArray =getFelds(searchVo.getResultType());;
            List<String> highlightFields=new ArrayList<>();
            if(StringUtil.isNotEmpty(searchVo.getKeyword())) {
                highlightFields = Arrays.stream(fieldArray).distinct().collect(Collectors.toList());
                fields = new Field[highlightFields.size()];
                for (int x = 0; x < highlightFields.size(); x++) {
                    fields[x] = new Field(highlightFields.get(x)).preTags(EsConstants.ES_PRE_TAGS)
                            .postTags(EsConstants.ES_POST_TAGS).fragmentSize(100).numOfFragments(5);
                }
            }
            setIndex(nativeSearchQueryBuilder,searchVo.getResultType());
            SearchQuery searchQuery =nativeSearchQueryBuilder.withPageable(pageable)
                    .withHighlightFields(fields).build();
            log.info(searchQuery.getQuery().toString());
            log.info(searchQuery.getElasticsearchSorts().toString());
            List<String> finalHighlightFields = highlightFields;
            return elasticsearchTemplate.queryForPage(searchQuery, SearchDto.class, new SearchResultMapper() {
                @Override
                public <T> AggregatedPage<T> mapResults(SearchResponse response, Class<T> clazz, Pageable pageable) {
                    ArrayList<SearchDto> datas = new ArrayList<SearchDto>();
                    SearchHits hits = response.getHits();
                    long total = hits.getTotalHits();
                    if (hits.getHits().length <= 0) {
                        return null;
                    }
                    for (SearchHit searchHit : hits) {
                        SearchDto data = JSONObject.parseObject(JSONObject.toJSONString(searchHit.getSourceAsMap()), SearchDto.class);
                        Class<? extends SearchDto> dataClass = data.getClass();
                        Map<String, HighlightField>  highlightMap = searchHit.getHighlightFields();
                        //获取高亮字段
                        if (StringUtil.isNotEmpty(highlightMap)){
                            for (String field: finalHighlightFields){
                                try {
                                    StringBuilder builder=new StringBuilder();
                                    HighlightField highlightField = highlightMap.get(field);
                                    if (StringUtil.isNotNull(highlightField)){
                                        Text[] fragments = highlightField.fragments();
                                        if (StringUtil.isNotEmpty(fragments)){
                                            for (Text text:fragments){
                                                builder.append(text.toString());
                                            }
                                        }
                                        if (StringUtil.isNotEmpty(builder.toString())){
                                            java.lang.reflect.Field fie = dataClass.getDeclaredField(field);
                                            fie.setAccessible(true);
                                            String str = builder.toString();
                                            str= HighlightUtils.higNumber(str,searchVo.getKeyword());
                                            if (field.equals("fullText")||field.equals("bookDesc")){
                                                int i = str.indexOf(EsConstants.ES_PRE_TAGS);
                                                if (i>EsConstants.ES_HIGHLIGHT_TEXT){
                                                    str=str.replace(str.substring(0,(i-EsConstants.ES_HIGHLIGHT_TEXT)),"");
                                                }
                                            }
                                            fie.set(data,str);
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("error",e);
                                }
                            }
                        }
                        datas.add(data);
                    }
                    if (datas.size() > 0) {
                        return new AggregatedPageImpl<T>((List<T>) datas,pageable,total);
                    }
                    return null;
                }

                @Override
                public <T> T mapSearchHit(SearchHit searchHit, Class<T> type) {
                    return null;
                }
            });
        }catch(Exception e) {
            log.error("查询异常，",e);
        }
        return null;
    }

    /**
     * 高级检索
     * @param searchVo
     * @param pageable
     * @return
     */
    @Override
    public Page<?> findAdvanceSearchList(AdvanceSearchVo searchVo, Pageable pageable) {
        try {
            BoolQueryBuilder boolQueryBuilder = buildAdvanceSearchQuery(searchVo);
            NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
            //设置排序
            String orderByColumn = searchVo.getOrderByColumn();
            String isAsc = searchVo.getIsAsc();
            FieldSortBuilder fieldSortBuilder=null;
            ScoreSortBuilder scoreSortBuilder = null;
            if(StringUtil.isEmpty(orderByColumn)||orderByColumn.equals("default")) {
                scoreSortBuilder = SortBuilders.scoreSort().order(SortOrder.DESC);
            }else {
                if(StringUtil.isEmpty(isAsc)||StringUtil.equalsIgnoreCase(isAsc, "asc")) {
                    fieldSortBuilder = SortBuilders.fieldSort(orderByColumn).order(SortOrder.ASC);
                }else {
                    fieldSortBuilder = SortBuilders.fieldSort(orderByColumn).order(SortOrder.DESC);
                }
            }
            if (StringUtil.isNotNull(fieldSortBuilder)){
                nativeSearchQueryBuilder.withSort(fieldSortBuilder);
            }else if (StringUtil.isNotNull(scoreSortBuilder)){
                nativeSearchQueryBuilder.withSort(scoreSortBuilder);
            }
            //设置高亮
            Field[] fields =null;
            List<String> highlightFields=new ArrayList<>();
            List<TSearchVo> searchList = searchVo.getSearchList();
            if(StringUtil.isNotEmpty(searchList)) {
                highlightFields =searchList.stream().map(TSearchVo::getQueryField)
                        .distinct().collect(Collectors.toList());
                fields = new Field[highlightFields.size()];
                for (int x = 0; x < highlightFields.size(); x++) {
                    fields[x] = new Field(highlightFields.get(x)).preTags(EsConstants.ES_PRE_TAGS)
                            .postTags(EsConstants.ES_POST_TAGS).fragmentSize(100).numOfFragments(5);
                }
            }
            nativeSearchQueryBuilder.withQuery(boolQueryBuilder);
            setIndex(nativeSearchQueryBuilder,searchVo.getResultType());
            SearchQuery searchQuery = nativeSearchQueryBuilder.withPageable(pageable)
                    .withHighlightFields(fields).build();
            log.info(searchQuery.getQuery().toString());
            log.info(searchQuery.getElasticsearchSorts().toString());
            List<String> finalHighlightFields = highlightFields;
            return elasticsearchTemplate.queryForPage(searchQuery, SearchDto.class, new SearchResultMapper() {
                @Override
                public <T> AggregatedPage<T> mapResults(SearchResponse response, Class<T> clazz, Pageable pageable) {
                    ArrayList<SearchDto> datas = new ArrayList<SearchDto>();
                    SearchHits hits = response.getHits();
                    long total = hits.getTotalHits();
                    if (hits.getHits().length <= 0) {
                        return null;
                    }
                    for (SearchHit searchHit : hits) {
                        SearchDto data = JSONObject.parseObject(JSONObject.toJSONString(searchHit.getSourceAsMap()), SearchDto.class);
                        Class<? extends SearchDto> dataClass = data.getClass();
                        Map<String, HighlightField>  highlightMap = searchHit.getHighlightFields();
                        //获取高亮字段
                        if (StringUtil.isNotEmpty(highlightMap)){
                            for (String field: finalHighlightFields){
                                try {
                                    StringBuilder builder=new StringBuilder();
                                    HighlightField highlightField = highlightMap.get(field);
                                    if (StringUtil.isNotNull(highlightField)){
                                        Text[] fragments = highlightField.fragments();
                                        if (StringUtil.isNotEmpty(fragments)){
                                            for (Text text:fragments){
                                                builder.append(text.toString());
                                            }
                                        }
                                        if (StringUtil.isNotEmpty(builder.toString())){
                                            java.lang.reflect.Field fie = dataClass.getDeclaredField(field);
                                            fie.setAccessible(true);
                                            String str = builder.toString();
                                            List<TSearchVo> collect = searchList.stream().filter(f -> field.equals(f.getQueryField())).collect(Collectors.toList());
                                            for (TSearchVo vo:collect){
                                                str= HighlightUtils.higNumber(str,vo.getKeyword());
                                            }
                                            if (field.equals("fullText")||field.equals("bookDesc")){
                                                int i = str.indexOf(EsConstants.ES_PRE_TAGS);
                                                if (i>EsConstants.ES_HIGHLIGHT_TEXT){
                                                    str=str.replace(str.substring(0,(i-EsConstants.ES_HIGHLIGHT_TEXT)),"");
                                                }
                                            }
                                            fie.set(data,str);
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("error",e);
                                }
                            }
                        }
                        datas.add(data);
                    }
                    if (datas.size() > 0) {
                        return new AggregatedPageImpl<T>((List<T>) datas,pageable,total);
                    }
                    return null;
                }

                @Override
                public <T> T mapSearchHit(SearchHit searchHit, Class<T> type) {
                    return null;
                }
            });
        }catch(Exception e) {
            log.error("查询异常，",e);
        }
        return null;
    }

    @Override
    public List<Navigation> findAggregationSearch(SearchVo searchVo, String field) {

        List<Navigation> collect=new ArrayList<>();
        try {
            BoolQueryBuilder boolQueryBuilder = buildGeneralSearchQuery(searchVo);

            NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
            nativeSearchQueryBuilder.withQuery(boolQueryBuilder);
            setIndex(nativeSearchQueryBuilder,searchVo.getResultType());
            Script script = new Script("doc['"+field+"'].values");
            TermsAggregationBuilder aggrega = AggregationBuilders.terms(field).script(script).size(Integer.MAX_VALUE).order(BucketOrder.count(false));
            nativeSearchQueryBuilder.addAggregation(aggrega);
            SearchQuery searchQuery=nativeSearchQueryBuilder.build();
            //执行分组
            Aggregations aggregations = elasticsearchTemplate.query(searchQuery, new ResultsExtractor<Aggregations>() {
                @Override
                public Aggregations extract(SearchResponse response) {
                    return response.getAggregations();
                }
            });
            Terms terms = (Terms)aggregations.get(field);
            collect = terms.getBuckets().stream().map(t ->
                    new Navigation(t.getKeyAsString(), t.getDocCount(),t.getKeyAsString())).collect(Collectors.toList());
        } catch (Exception e) {
            log.info("error,",e);
        }
        return collect;
    }


    @Override
    public List<Navigation> findAggregationSearch(AdvanceSearchVo searchVo, String field) {

        List<Navigation> collect=new ArrayList<>();
        try {
            BoolQueryBuilder boolQueryBuilder = buildAdvanceSearchQuery(searchVo);
            NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
            nativeSearchQueryBuilder.withQuery(boolQueryBuilder);
            setIndex(nativeSearchQueryBuilder,searchVo.getResultType());
            Script script = new Script("doc['"+field+"'].values");
            TermsAggregationBuilder aggrega = AggregationBuilders.terms(field).script(script).size(Integer.MAX_VALUE).order(BucketOrder.count(false));
            nativeSearchQueryBuilder.addAggregation(aggrega);
            SearchQuery searchQuery=nativeSearchQueryBuilder.build();
            //执行分组
            Aggregations aggregations = elasticsearchTemplate.query(searchQuery, new ResultsExtractor<Aggregations>() {
                @Override
                public Aggregations extract(SearchResponse response) {
                    return response.getAggregations();
                }
            });
            Terms terms = (Terms)aggregations.get(field);
            collect = terms.getBuckets().stream().map(t ->
                    new Navigation(t.getKeyAsString(), t.getDocCount(),t.getKeyAsString())).collect(Collectors.toList());
        } catch (Exception e) {
            log.info("error,",e);
        }
        return collect;
    }

    private void setIndex(NativeSearchQueryBuilder builder, Integer resultType) {
        if (resultType.equals(1)){
            builder.withIndices(EsConstants.ES_INDEX_NAME_HUANGHE_MENUS)
                    .withTypes(EsConstants.ES_TYPE_HUANGHE_MENUS);
        }else {
            builder.withIndices(EsConstants.ES_INDEX_NAME_HUANGHE_BOOKS)
                    .withTypes(EsConstants.ES_TYPE_HUANGHE_BOOKS);
        }
    }



    /**
     * 快速检索查询总数量
     * @param searchVo
     * @return
     */
    @Override
    public Long querySearchCount(SearchVo searchVo) {
        BoolQueryBuilder boolQueryBuilder = buildGeneralSearchQuery(searchVo);
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(boolQueryBuilder);
        setIndex(nativeSearchQueryBuilder,searchVo.getResultType());
        NativeSearchQuery searchQuery = nativeSearchQueryBuilder.build();
        return elasticsearchTemplate.count(searchQuery);
    }

    /**
     * 高级检索查询总数量
     * @param searchVo
     * @return
     */
    @Override
    public Long querySearchCount(AdvanceSearchVo searchVo) {
        BoolQueryBuilder boolQueryBuilder = buildAdvanceSearchQuery(searchVo);
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(boolQueryBuilder);
        setIndex(nativeSearchQueryBuilder,searchVo.getResultType());
        NativeSearchQuery searchQuery = nativeSearchQueryBuilder.build();
        return elasticsearchTemplate.count(searchQuery);
    }

    /**
     * 快速检索检索条件
     * @param searchVo
     * @return
     */
    private BoolQueryBuilder buildGeneralSearchQuery(SearchVo searchVo) {
        String[] fields = getFelds(searchVo.getResultType());
        BoolQueryBuilder boolQueryBuilder = EsUtil.boolQueryBuilder(searchVo.getKeyword(), fields);
        //状态 0-下架 1-上架
        boolQueryBuilder.must(QueryBuilders.termQuery("proStatus","1"));
        if (searchVo.getResultType().equals(1)){
            boolQueryBuilder.must(QueryBuilders.existsQuery("fullText"));
        }
        //分类
        if (StringUtil.isNotEmpty(searchVo.getResourceClassesIdList())){
            List<String> resourceTypeIdList = searchVo.getResourceClassesIdList();
            List<String> list=new ArrayList<>();
            for (String id:resourceTypeIdList){
                List<String> typeList=tConfigClassicTreeService.getSearchId(id);
                list.addAll(typeList);
            }
            boolQueryBuilder.must(QueryBuilders.termsQuery("resourceClassesId",list));
        }
        return boolQueryBuilder;
    }

    /**
     * 高级检索检索条件
     * @param searchVo
     * @return
     */
    private BoolQueryBuilder buildAdvanceSearchQuery(AdvanceSearchVo searchVo) {
        BoolQueryBuilder boolQueryBuilder = boolQuery();
        if(StringUtil.isNotEmpty(searchVo.getSearchList())) {
            BoolQueryBuilder kuohaowaiQueryBuilder = boolQuery();
            List<TSearchVo> searchList = searchVo.getSearchList();
            for(int i=0;i<searchList.size();i++) {
                TSearchVo vo = searchList.get(i);
                String field = vo.getQueryField();//查询的字段
                String keyword = vo.getKeyword();//关键词
                log.info("==>检索条件：{}",keyword);
                if (StringUtil.isNotEmpty(keyword)){
                    String combincc = vo.getKeywordType();// 组合下一个条件
                    if (StringUtil.isEmpty(combincc)){
                        combincc="and";
                    }
                    if ((i+1)<searchList.size()){
                        String keywordType = searchList.get(i + 1).getKeywordType();
                        if ("or".equals(keywordType)){
                            combincc=keywordType;
                        }
                    }
                    BoolQueryBuilder queryBuilder=boolQuery();
                    List<String> keys = Arrays.stream(keyword.split("\\+")).distinct().collect(Collectors.toList());
                    for (String key:keys){
                        queryBuilder.should(QueryBuilders.matchPhraseQuery(field, key).slop(0));
                        List<String> fencis = IkUtils.fenciSmart(key.trim());
                        for(String fenci : fencis) {
                            log.info("==>分词结果 {}", fenci);
                            queryBuilder.should(QueryBuilders.matchPhraseQuery(field,fenci.trim()).slop(0));
                        }
                    }
                    switch (combincc) {
                        case "and":
                            kuohaowaiQueryBuilder.must(queryBuilder);
                            break;
                        case "or":
                            kuohaowaiQueryBuilder.should(queryBuilder);
                            break;
                        case "not":
                            kuohaowaiQueryBuilder.mustNot(queryBuilder);
                            break;
                        default:
                            break;
                    }
                }
            }
            boolQueryBuilder.must(kuohaowaiQueryBuilder);
        }
        //状态 0-下架 1-上架
        boolQueryBuilder.must(QueryBuilders.termQuery("displayStatus", "1"));
        if (searchVo.getResultType().equals(1)){
            boolQueryBuilder.must(QueryBuilders.existsQuery("fullText"));
        }
        //分类
        if (StringUtil.isNotEmpty(searchVo.getResourceClassesIdList())){
            List<String> resourceTypeIdList = searchVo.getResourceClassesIdList();
            List<String> list=new ArrayList<>();
            for (String id:resourceTypeIdList){
                List<String> typeList=tConfigClassicTreeService.getSearchId(id);
                list.addAll(typeList);
            }
            boolQueryBuilder.must(QueryBuilders.termsQuery("resourceClassesId",list));
        }
        return boolQueryBuilder;
    }

    private String[] getFelds(Integer resultType) {
        if (resultType==1){
            //全文结果
            return new String[]{"menuName","fullText"};
        }else {
            //图书结果
            return new String[]{"bookName","mainResponsibility","bookDesc"};
        }
    }
}
