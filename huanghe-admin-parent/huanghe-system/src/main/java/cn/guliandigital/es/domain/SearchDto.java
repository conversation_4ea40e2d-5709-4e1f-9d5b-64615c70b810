package cn.guliandigital.es.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value="检索列表应答实体类",description="检索列表应答实体类")
public class SearchDto implements Serializable {

    @ApiModelProperty(value="图书Id")
    private String bookId;

    @ApiModelProperty(value="章节Id")
    private String menuId;

    @ApiModelProperty(value="章节名称")
    private String menuName;

    @ApiModelProperty(value="章节全路径")
    private String fullMenuName;

    @ApiModelProperty(value="章节起始页码")
    private Integer startPageNo;

    @ApiModelProperty(value="正文内容")
    private String fullText;

    @ApiModelProperty(value="书名")
    private String bookName;

    @ApiModelProperty(value="文献编号")
    private String uniqueId;

    @ApiModelProperty(value="所属卷")
    private String resourceClasses;

    @ApiModelProperty(value="主编")
    private String mainResponsibility;

    @ApiModelProperty(value="出版社")
    private String publisher;

    @ApiModelProperty(value="出版时间")
    private String publishDate;

    @ApiModelProperty(value="版次")
    private String revision;

    @ApiModelProperty(value="主题词-词频")
    private String subjectWord;

    @ApiModelProperty(value="主题词-内容")
    private String subjectText;

    @ApiModelProperty(value="简介")
    private String bookDesc;

    @ApiModelProperty(value="资源类型,T-全文，PDF-pdf")
    private String resourceType;

    @ApiModelProperty(value="图书封面路径")
    private String coverUrl;

    @ApiModelProperty(value="是否加入书架，true-已加入,false-未加入")
    private Boolean haveAddshelf;

    @ApiModelProperty(value = "书架Id")
    private String shelfClassId;
}
