package cn.guliandigital.es.domain;

import cn.guliandigital.common.utils.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value="快速检索请求参数实体类",description="快速检索检索请求参数实体类")
public class SearchVo implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value="查询词语,多个词语用+分隔",required=true)
    private String keyword;

    @ApiModelProperty(value="结果类型, 1-全文结果，2-图书结果",required=true)
    private Integer resultType;

    @ApiModelProperty(value="页码",required=true)
    private Integer pageNum;

    @ApiModelProperty(value="每页条数",required=true)
    private Integer pageSize;

    @ApiModelProperty(value="排序字段,default-相关度,publishDate-公布日期")
    private String orderByColumn;

    @ApiModelProperty(value="排序方式,asc-正序，desc-倒序")
    private String isAsc;

    @ApiModelProperty(value="分类id")
    private List<String> resourceClassesIdList;

    public Integer getPageNum() {
        if (StringUtil.isNull(this.pageNum)){
            return 1;
        }
        return pageNum;
    }

    public Integer getPageSize() {
        if (StringUtil.isNull(this.pageSize)){
            return 10;
        }
        return pageSize;
    }

    public Integer getResultType() {
        if (StringUtil.isNull(this.resultType)){
            return 1;
        }
        return resultType;
    }
}