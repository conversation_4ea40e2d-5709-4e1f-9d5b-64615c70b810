package cn.guliandigital.order.domain;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonInclude;
import cn.guliandigital.common.annotation.Excel;
import cn.guliandigital.common.core.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 数据库订单管理对象 t_pro_order
 * 
 * <AUTHOR>
 * @date 2020-09-17
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class TProOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    public String getOrderCreateName() {
        return orderCreateName;
    }

    public void setOrderCreateName(String orderCreateName) {
        this.orderCreateName = orderCreateName;
    }

    private  String orderCreateName;
    /** 产品包编号 */
    @Excel(name = "订单编号",sort = 1)
    private String orderCode;

    /** 机构ID */
//    @Excel(name = "机构ID")
    private String orgId;

    /** 开通数据库 */
//    @Excel(name = "开通数据库")
    private String ktDatabase;

    /** 订单状态 0-未开通 1-开通授权 */
    @Excel(name = "订单状态",readConverterExp = "0=已停止,1=已授权",sort = 3)
    private String orderStatus;

    /** 产品价格 */
//    @Excel(name = "产品价格")
    private BigDecimal price;

    /** 折扣 */
//    @Excel(name = "折扣")
    private BigDecimal discount;

    /** 最后价格 */
//    @Excel(name = "最后价格")
    private BigDecimal finalPrice;

    /** 授权方式 */
//    @Excel(name = "授权方式")
    private String authMethod;

    /** 授权开始时间 */
    @Excel(name = "授权开始时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String authStartTime;

    /** 授权结束时间 */
    @Excel(name = "授权结束时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String authEndTime;

    /** 简介 */
//    @Excel(name = "简介")
    private String orderDesc;

    /** $column.columnComment */
//    @Excel(name = "简介")
    private String createbyId;

    /** 创建人 */
//    @Excel(name = "创建人")
    private String createbyName;

    /** $column.columnComment */
//    @Excel(name = "创建人")
    private String updatebyId;

    /** 更新人 */
//    @Excel(name = "更新人")
    private String updatebyName;

    /** 删除标识 */
//    @Excel(name = "删除标识")
    private Integer delFlag;

    @Excel(name = "机构名称",sort = 2)
    private String orgName;

    @Excel(name = "订单类型",readConverterExp = "0=试用,1=正式",sort = 4)
    private Integer orderType;


    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Excel(name = "下单时间",dateFormat = "yyyy-MM-dd HH:mm:ss",sort = 7)
    private Date createTime;

    private String[] ids;


    private String[] searchTime;


    /** 搜索参数 */
    private String startTime;

    /** 搜索参数 */
    private String endTime;




    public void setAuthStartTime(String authStartTime) {
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.CHINA);
        SimpleDateFormat yyyy=new SimpleDateFormat("yyyy-MM-dd");
        try {
            this.authStartTime = yyyy.format(format.parse(authStartTime));
        } catch (ParseException e) {

          String date =authStartTime.substring(0,10);
            this.authStartTime=date;
        }
    }

    public void setAuthEndTime(String authEndTime) {
        SimpleDateFormat format=new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.CHINA);
        SimpleDateFormat yyyy=new SimpleDateFormat("yyyy-MM-dd");
        try {
            this.authEndTime = yyyy.format(format.parse(authEndTime));
        } catch (ParseException e) {
            String date =authEndTime.substring(0,10);
            this.authEndTime=date;
        }
    }
}
