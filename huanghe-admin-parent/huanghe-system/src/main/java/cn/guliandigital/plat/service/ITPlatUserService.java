package cn.guliandigital.plat.service;

import java.util.List;

import cn.guliandigital.common.core.domain.model.WebLoginBody;
import cn.guliandigital.plat.domain.TPlatUser;

/**
 * 平台用户Service接口
 * 
 * <AUTHOR>
 * @date 2020-09-15
 */
public interface ITPlatUserService 
{
	
	boolean checkLimit(TPlatUser user,String ip,String dbId);
	
	TPlatUser selectUserById(String id);
	
	String getOrgId(TPlatUser user,String ip);
    /**
     * 查询平台用户
     * 
     * @param id 平台用户ID
     * @return 平台用户
     */
        TPlatUser selectTPlatUserById(String id);

    /**
     * 查询平台用户列表
     * 
     * @param tPlatUser 平台用户
     * @return 平台用户集合
     */
    List<TPlatUser> selectTPlatUserList(TPlatUser tPlatUser);

    /**
     * 新增平台用户
     * 
     * @param tPlatUser 平台用户
     * @return 结果
     */
    int insertTPlatUser(TPlatUser tPlatUser) throws Exception;

    /**
     * 修改平台用户
     * 
     * @param tPlatUser 平台用户
     * @return 结果
     */
    int updateTPlatUser(TPlatUser tPlatUser) throws Exception;

    /**
     * 批量删除平台用户
     * 
     * @param ids 需要删除的平台用户ID
     * @return 结果
     */
    int deleteTPlatUserByIds(String[] ids);

    /**
     * 删除平台用户信息
     * 
     * @param id 平台用户ID
     * @return 结果
     */
    int deleteTPlatUserById(String id);

    /**
     * 平台用户登录
     *
     * @param body 登录信息实体
     * @return 用户对象
     */
    TPlatUser userLogin(WebLoginBody body) throws Exception;

    TPlatUser getUserInfoByAccessToken(String accessToken, String appUserid, String refreshToken, Integer expiresIn) throws Exception;
    void refreshAccessToken(String openid, String unionid,String refreshtoken) throws Exception;
    
    TPlatUser selectByUnionid(String appUserid);


    TPlatUser getQQUserInfo(String accessToken, String appUserid, String refreshToken, Integer expiresIn) throws Exception;


    void refreshQQAccessToken(String appUserid, String refreshToken);

    int updateTPlatUserByBr(TPlatUser tPlatUser);

    List<TPlatUser> selectTPlatUserListByName(TPlatUser tPlatUser);
}
