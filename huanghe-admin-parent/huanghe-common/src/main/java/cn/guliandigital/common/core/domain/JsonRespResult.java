package cn.guliandigital.common.core.domain;

import java.io.Serializable;

import cn.guliandigital.common.constant.HttpStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 操作消息提醒
 * 
 * <AUTHOR>
 */
@Data
public class JsonRespResult<T> implements Serializable {

	@ApiModelProperty(value = "返回码：正确200, 500错误")
	private int code;
	
	@ApiModelProperty(value = "返回消息")
	private String msg;
	
	@ApiModelProperty(value = "返回数据内容")
	private T data;
	@ApiModelProperty(value = "总条目数")
	private long total;


	public JsonRespResult() {
		
	}
	

	public JsonRespResult(int code, String msg, T data) {
		this.code = code;
		this.msg = msg;
		this.data = data;
	}

	public JsonRespResult<T> success(T data) {		
		this.code = HttpStatus.SUCCESS;
		this.msg = "操作成功";
		this.data = data;
		return this;
	}

	public JsonRespResult<T> success(String msg, T data) {
		this.code = HttpStatus.SUCCESS;
		this.msg = msg;
		this.data = data;
		return this;
	}
	
	public JsonRespResult<T> success(String msg) {		
		this.code = HttpStatus.SUCCESS;
		this.msg = msg;
		this.data = null;
		return this;
	}

	public JsonRespResult<T> error(String msg) {		
		this.code = HttpStatus.ERROR;
		this.msg = msg;
		this.data = null;
		return this;
	}
	
	public JsonRespResult<T> error(int code, String msg) {		
		this.code = code;
		this.msg = msg;
		this.data = null;
		return this;
	}
	
}
