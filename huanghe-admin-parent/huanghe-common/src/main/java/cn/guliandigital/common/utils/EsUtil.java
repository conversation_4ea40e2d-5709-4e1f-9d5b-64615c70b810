package cn.guliandigital.common.utils;

import cn.guliandigital.common.constant.RedisConstants;
import cn.guliandigital.common.core.redis.RedisCache;
import cn.guliandigital.common.utils.word.IkUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.common.lucene.search.function.CombineFunction;
import org.elasticsearch.common.lucene.search.function.FunctionScoreQuery;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders;
import org.elasticsearch.index.query.functionscore.ScriptScoreFunctionBuilder;
import org.elasticsearch.index.search.MatchQuery;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.sort.ScoreSortBuilder;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;


@Slf4j
public class EsUtil {

    public static void scoreSort(NativeSearchQueryBuilder nativeSearchQueryBuilder, BoolQueryBuilder boolQueryBuilder,List<String> paramList){
        //全文结果
        //获取排序参数
        Map<String, Object> params = new HashMap<>();
        List<String> result=new ArrayList<>();
        for (String key :paramList){
            result.addAll(Arrays.asList(key.split("")));
        }
        List<String> collect = result.stream().distinct().collect(Collectors.toList());
        params.put("keywords", collect);
        Script scoreScript = new Script(
                ScriptType.INLINE,
                "painless",
                "def scores = 0;" +
                        "if (doc.containsKey('menuName') && !doc['menuName'].empty) {"+
                        "  String menuName = doc['menuName'].value;" +
                        "  for (keyword in params.keywords) {" +
                        "    if (menuName.contains(keyword)) {" +
                        "      scores += 200;"+
                        "    }" +
                        "  }" +
                        "}"+
                        "return scores;",
                params
        );
        //Script scoreScript = new Script("def scores=0;if(doc['menuName'].length>=1){scores+=100;}if(doc['fullText'].length>=1){scores +=1;}return scores;");
        
        ScriptScoreFunctionBuilder weight = ScoreFunctionBuilders.scriptFunction(scoreScript);

        FunctionScoreQueryBuilder.FilterFunctionBuilder[] filterFunctionBuilders = new FunctionScoreQueryBuilder.FilterFunctionBuilder[1];
        filterFunctionBuilders[0] = new FunctionScoreQueryBuilder.FilterFunctionBuilder(weight);

        FunctionScoreQueryBuilder query = QueryBuilders.functionScoreQuery(boolQueryBuilder,filterFunctionBuilders)
                .boostMode(CombineFunction.SUM)
                .scoreMode(FunctionScoreQuery.ScoreMode.SUM);
        // 排序条件
        ScoreSortBuilder sortBuilder = SortBuilders.scoreSort();
        nativeSearchQueryBuilder.withQuery(query).withSort(sortBuilder.order(SortOrder.DESC));
    }


    //获取排序用到的参数
    public static List<String> scoreSortParams(String keyword) {
        if (StringUtil.isNotEmpty(keyword)){
            String[] keys=null;
            List<String> list=new ArrayList<>();
            if(StringUtil.contains(keyword, "+")){
                keys = keyword.split("\\+");
            }else {
                keys = keyword.split("\\s+");
            }
            for (String _key : keys){
                if (StringUtil.isEmpty(_key)){
                    continue;
                }
                //分词处理
                List<String> fencis = IkUtils.fenciSmart(_key.trim());
                //原来的检索词
                fencis.add(_key.trim());
                //标题需要删掉前后的标点  特殊处理
                String nopun_key = deleteBeginAndEndPunctuation(_key.trim());
                fencis.add(nopun_key);
                list.addAll(fencis);
            }
            //去重
            return list.stream().distinct().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public static BoolQueryBuilder boolQueryBuilder(String keyword,String[] fields){
        BoolQueryBuilder boolQueryBuilder = boolQuery();
        if(StringUtil.isNotEmpty(keyword)) {
            String[] keys = null;
            BoolQueryBuilder queryBuilder = boolQuery();
            if(StringUtil.contains(keyword, "+")) {
                //二次检索
                keys = keyword.split("\\+");
                for (String _key : keys){
                    //分词处理
                    List<String> fencis = IkUtils.fenciSmart(_key.trim());
                    //原来的检索词
                    fencis.add(_key.trim());
                    //标题需要删掉前后的标点  特殊处理
                    String nopun_key = deleteBeginAndEndPunctuation(_key.trim());
                    fencis.add(nopun_key);
                    //去重
                    List<String> fencilist = fencis.stream().distinct().collect(Collectors.toList());
                    BoolQueryBuilder boolQuery = boolQuery();
                    for(String fenci : fencilist) {
                        log.info("==>分词结果 {}", fenci);
                        boolQuery.should(QueryBuilders.multiMatchQuery(fenci.trim(), fields).type(MatchQuery.Type.PHRASE).field("menuName", 1f).field("fullText", 1f).slop(0));
                    }
                    queryBuilder.must(boolQuery);
                }
            }else {
            	//普通检索
                keys = keyword.split("\\s+");
                for (String _key : keys){
                    //分词处理
                    List<String> fencis = IkUtils.fenciSmart(_key.trim());
                    //原来的检索词
                    fencis.add(_key.trim());
                    //标题需要删掉前后的标点  特殊处理
                    String nopun_key = deleteBeginAndEndPunctuation(_key.trim());
                    fencis.add(nopun_key);
                    //去重
                    List<String> fencilist = fencis.stream().distinct().collect(Collectors.toList());
                    for(String fenci : fencilist) {
                        log.info("==>分词结果 {}", fenci);
                        queryBuilder.should(QueryBuilders.multiMatchQuery(fenci.trim(), fields).type(MatchQuery.Type.PHRASE).field("menuName", 1f).field("fullText", 1f).slop(0));
                    }
                }
            }
            boolQueryBuilder.must(queryBuilder);
        }
        return boolQueryBuilder;
    }
    public static String deleteBeginAndEndPunctuation(String s) {
        // 检查是否为空
        if (Strings.isNullOrEmpty(s)) {
            return s;
        } 
        // 标点符号判断
        int beginIndex = 0;
        int endIndex = s.length() - 1;
        while (beginIndex < endIndex && isPunctuation(s.charAt(beginIndex))) {
            beginIndex++;
        }
        while (beginIndex < endIndex && isPunctuation(s.charAt(endIndex))) {
            endIndex--;
        }
 
        // 返回结果
        return s.substring(beginIndex, endIndex + 1);
    }
 
    public static boolean isPunctuation(char ch) {
        // 这里可以扩展标点符号的判断条件
        return !Character.isLetterOrDigit(ch);
    }
}
