package cn.guliandigital.common.utils.word;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import java.io.StringReader;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
public class IkUtils {

	public static List<String> fenciSmart(String word){
		
		List<String> datas = Lists.newArrayList();
		try {
		
			if(StringUtils.length(word) == 1) {
				return datas;
			}
			
			// 创建IK分词器，true表示使用智能分词模式
	        IKSegmenter ikSegmenter = new IKSegmenter(new StringReader(word), true);
	
	        Lexeme lexeme;
	        while ((lexeme = ikSegmenter.next()) != null) {
	        	log.info(lexeme.getLexemeText());
	        	datas.add(lexeme.getLexemeText());
	        }
			if(CollectionUtils.isNotEmpty(datas)) {
				datas = datas.stream().distinct().collect(Collectors.toList());
			}
		}catch(Exception e) {
			log.error("error,", e);
		}
		return datas;
	}
}
