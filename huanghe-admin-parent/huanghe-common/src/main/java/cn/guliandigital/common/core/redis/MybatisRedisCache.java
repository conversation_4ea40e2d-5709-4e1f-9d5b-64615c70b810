package cn.guliandigital.common.core.redis;

import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import org.apache.ibatis.cache.Cache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import cn.guliandigital.common.utils.spring.SpringUtils;

import lombok.extern.slf4j.Slf4j;


@Slf4j
public class MybatisRedisCache implements Cache{

		// 读写锁
	    private final ReadWriteLock readWriteLock = new ReentrantReadWriteLock(true);
	 
	    private final Long expireTime = 3L;
	    
	    @Autowired
	    private RedisTemplate<String, Object> template;
	    
	    private String id;
	 
	    public MybatisRedisCache(final String id) {
	        if (id == null) {
	            throw new IllegalArgumentException("Cache instances require an ID");
	        }
	        log.info("Redis Cache id " + id);
	        this.id = id;       
	    }
	 
	    public void setDatabase(int database){
	    	 template = (RedisTemplate<String, Object>) SpringUtils.getBean("redisTemplate");
//	    	 LettuceConnectionFactory factory = (LettuceConnectionFactory)template.getConnectionFactory();
//	         factory.setDatabase(database);
//	         template.setConnectionFactory(factory);
//	         factory.resetConnection();
		}
	    
	    @Override
	    public String getId() {
	        return this.id;
	    }
	 
	    @Override
	    public void putObject(Object key, Object value) {
	    	setDatabase(8);
	        if (value != null) {
	            // 向Redis中添加数据，有效时间是2天
	        	template.opsForValue().set(key.toString(), value, expireTime, TimeUnit.DAYS);
	        }
	    }
	 
	    @Override
	    public Object getObject(Object key) {
	        try {
	        	setDatabase(8);
	            if (key != null) {
	                Object obj = template.opsForValue().get(key.toString());
	                return obj;
	            }
	        } catch (Exception e) {
	            log.error("redis ",e);
	        }finally {
	        	//template.killClient(Constants.REDIS_HOST, Constants.REDIS_PORT);
	        }
	        return null;
	    }
	 
	    @Override
	    public Object removeObject(Object key) {
	        try {
	        	setDatabase(8);
	            if (key != null) {
	            	template.delete(key.toString());
	            }
	        } catch (Exception e) {
	        	 log.error("redis ",e);
	        }
	        return null;
	    }
	 
	    @Override
	    public void clear() {
	    	log.info("清空缓存");
	        try {
	        	setDatabase(8);
	            Set<String> keys = template.keys("*:" + this.id + "*");
	            if (!CollectionUtils.isEmpty(keys)) {
	            	template.delete(keys);
	            }
	        } catch (Exception e) {
	        	 log.error("redis ",e);
	        }
	    }
	 
	    @Override
	    public int getSize() {
	    	setDatabase(8);
	        Long size = (Long) template.execute(new RedisCallback<Long>() {
	            @Override
	            public Long doInRedis(RedisConnection connection) throws DataAccessException {
	                return connection.dbSize();
	            }
	        });
	        return size.intValue();
	    }
	 
	    @Override
	    public ReadWriteLock getReadWriteLock() {
	        return this.readWriteLock;
	    }

	
}
