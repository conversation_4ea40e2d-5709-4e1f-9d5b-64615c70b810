package cn.guliandigital.common.enums;

public enum InStatusEnum {

	//0-入库中 1-入库完成 2-入库失败
	
    ING("0","入库中"),    
    SUCCESS("1","入库完成") ,
    LOST("2","入库失败")  
    ;


    private final String code;
    private final String msg;

    InStatusEnum(String code, String msg)
    {
        this.code = code;
        this.msg = msg;
    }

    public String getCode()
    {
        return code;
    }

    public String getMsg()
    {
        return msg;
    }
    /**
     * 根据key获取value
     *
     * @param code
     * @return
     */
    public static String getValue(String code) {
        InStatusEnum[] imagetextTypeEnums = values();
        for (InStatusEnum imagetextTypeEnum : imagetextTypeEnums) {
            if (imagetextTypeEnum.getCode().equals(code)) {
                return imagetextTypeEnum.getMsg();
            }
        }
        return null;
    }

    /**
     * 根据value获取key
     *
     * @param message
     * @return
     */
    public static String getCode(String message) {
        InStatusEnum[] imagetextTypeEnums = values();
        for (InStatusEnum imagetextTypeEnum : imagetextTypeEnums) {
            if (imagetextTypeEnum.getMsg().equals(message)) {
                return imagetextTypeEnum.getCode();
            }
        }
        return null;
    }
}
