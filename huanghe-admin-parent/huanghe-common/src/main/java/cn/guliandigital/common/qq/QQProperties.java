package cn.guliandigital.common.qq;/**
 * <AUTHOR>
 * @Description
 * @Date 17:03
 * @param ${}:
 **/

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName qqProperties
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/10/19 17:03
 */
@Data
@Component
@ConfigurationProperties(prefix="qqconfig")
public class QQProperties {
    private String client_id;

    private String client_secret;

    private String redirect_uri;
}
