package cn.guliandigital.common.utils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import cn.guliandigital.common.constant.QuerySuffixConstant;

/**
 * condition工具类
 * 
 * <AUTHOR>
 *
 */
public class ConditionUtils {

	private Map<String, Object> condition;

	public ConditionUtils(Map<String, Object> condition) {
		this.condition = condition;
	}

	/**
	 * 初始化工具类中的condition属性
	 * 
	 * @return 工具类对象
	 */
	public static ConditionUtils condition() {
		return new ConditionUtils(new HashMap<>());
	}
	
	/**
	 * 将参数作为工具类中的condition属性的初始值
	 * 
	 * @param condition
	 * @return 工具类对象
	 */
	public static ConditionUtils condition(Map<String, Object> condition) {
		return new ConditionUtils(condition);
	}

	/**
	 * 添加一个元素
	 * 
	 * @param key
	 * @param value
	 * @return 工具类对象
	 */
	public ConditionUtils put(String key, Object value) {
		this.condition.put(key, value);
		return this;
	}
	
	public ConditionUtils putIfNotBlank(String key, String value) {
		if(StringUtils.isNotBlank(value)) {
			this.condition.put(key, value);
		}
		return this;
	}
	
	public ConditionUtils putIfNotNull(String key, Object value) {
		if(value != null) {
			this.condition.put(key, value);
		}
		return this;
	}
	
	/**
	 * {key} + StringConstant.IN_SUFFIX
	 * </br> "key" + "In" = "keyIn"
	 * @param <T>
	 * @param key
	 * @param value
	 * @return
	 */
	public <T> ConditionUtils in(String key, T...value) {
		this.condition.put(key + QuerySuffixConstant.IN_SUFFIX, value);
		return this;
	}
	
	/**
	 * {key} + StringConstant.IN_SUFFIX
	 * </br> "key" + "In" = "keyIn"
	 * @param <T>
	 * @param key
	 * @param value
	 * @return
	 */
	public <T> ConditionUtils in(String key, List<T> value) {
		this.condition.put(key + QuerySuffixConstant.IN_SUFFIX, value);
		return this;
	}
	
	public ConditionUtils like(String key, String value) {
		this.condition.put(key + QuerySuffixConstant.LIKE_SUFFIX, value);
		return this;
	}
	
	public ConditionUtils prefix(String key, String value) {
		this.condition.put(key + QuerySuffixConstant.PREFIX_SUFFIX, value);
		return this;
	}
	
	public ConditionUtils gte(String key, Integer value) {
		this.condition.put(key + QuerySuffixConstant.GTE_SUFFIX, value);
		return this;
	}
	
	public ConditionUtils lte(String key, Integer value) {
		this.condition.put(key + QuerySuffixConstant.LTE_SUFFIX, value);
		return this;
	}
	/**
	 * 将一个map中的所有元素添加到condition
	 * 
	 * @param map
	 * @return 工具类对象
	 */
	public ConditionUtils putAll(Map<String, Object> map) {
		this.condition.putAll(map);
		return this;
	}
	
	/**
	 * 将参数清空并作为工具类中的condition属性的初始值
	 * 
	 * @param condition
	 * @return 工具类对象
	 */
	public static ConditionUtils clear(Map<String, Object> condition) {
		condition.clear();
		return new ConditionUtils(condition);
	}

	/**
	 * 获取condition属性
	 * 
	 * @return 工具类中的condition属性, 未初始化工具类获取的是新的Map
	 */
	public Map<String, Object> getCondition() {
		if(this.condition == null) {
			return new HashMap<>();
		}
		return this.condition;
	}

	public static void main(String[] args) {
		Map<String, Object> condition1 = ConditionUtils.condition().put("1", "2").put("2", "1").getCondition();
		System.out.println(condition1);// {1=2, 2=1}
		Map<String, Object> condition2 = ConditionUtils.clear(condition1).put("3", "4").put("4", "5").getCondition();
		System.out.println(condition2);// {3=4, 4=5}
		System.out.println(condition1.equals(condition2)); // true
		Map<String, Object> condition3 = ConditionUtils.condition(condition2).put("1", "2").put("2", "1")
				.getCondition();
		System.out.println(condition3);// {1=2, 2=1, 3=4, 4=5}
		System.out.println(condition1.equals(condition3)); // true
		Map<String, Object> condition4 = ConditionUtils.condition().put("1", "1").put("2", "2").getCondition();
		System.out.println(condition4);// {1=1, 2=2}
		Map<String, Object> condition5 = ConditionUtils.condition().put("5", "6").put("6", "7").putAll(condition4)
				.getCondition();
		System.out.println(condition5);// {1=1, 2=2, 5=6, 6=7}
	}
}
