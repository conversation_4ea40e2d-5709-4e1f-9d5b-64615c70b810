package cn.guliandigital.common.utils.html;

import cn.guliandigital.common.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CompleteTagsUtils {

	static int idx = 0;
	
	public static void main(String[] args) throws Exception {
		String htmlCode = "吧傳鉄夜阅撴误赼麅垶据㒃发焔鉓蜙榽鬃阅裱㘎㝨翯攠㝇䖋頃阅䪍鰒䍡哈㚻芛琳㣥銚恸潿嘅㟂灟緄㒃黖㽌裷麅蟭䍡欭㒃㱍黋</p><p>【譗偆埰䍛袁缩㤯坍鉏蟭】</p><p>䬩坍鉏刮懁㟻嚇阅礴狂殙猳坆偆盗阅魅㒃䶲院䶺蜙雫頛鰒嵕阅蘠蒲医䌺粷䈆阅篘梏逮偆阅譀䘓诵遭珴阅磦撄拦秘据菍濈爃葌粂㣥啝醿㚊㻠琳鈢㽑马䣩礝嘂燔糣緄韫㜠䲻枝铢朋嗚䳏磦㓅䥬矮玟丣嚆䆬饀刮灌螤婁莵埱墲沄㻠芴魗賨懁䊻嘂梥㝇㠸没䴇鉏阅䇉";
		// htmlCode =
		// "事有死無二輙出此門者斬辝色慷慨士皆感泣又招諸將曰凡不爲紅巾者從我傅慶劉經乃以軍從充充竟以金陵府庫與其家渡江降虜有說飛俱叛而北者飛陽許之後虜犯溧陽飛遣人夜半馳至縣殺獲五百餘人生擒女眞漢兒并僞同知溧陽縣事渤海太師李撒八等一十二人及千戸留哥是月虜䧟建康杜充旣率麾下北去總領李梲及守臣陳邦光並降獨通判楊邦乂不從刺血書其衣裾曰寧作趙氏鬼不爲它邦臣虜觴二降臣於堂土立邦乂堂下邦乂熟視二降人曰天子以若扞城旣不能死又不能抗尙何靣目見我乎虜怒又命引去明日再引以見邦乂遥望兀木大駡曰若以夷狄而圖中原天能久假乎恨不磔汝萬叚虜怒剖腹取其心
		// 朝廷尋賜廟額曰褒忠謚曰忠";
		htmlCode = "测试<div class='biaoti1'>鈹片忺崣懥惕鼴懏椔䂳</div><p><span class=\"luokuan\">（樧罂颓糶垼牿砉釢贪撌㩦䥻邸蜊睹犫溠桶）䢞（䤌刏）緻䪔</span></p>";
		//htmlCode = "<p>䧫駈箝犸駈虇 祦㱗 祦闉 䨰堫趻 祦<span class=\"big02\">楸</span> 㞸薋踁 颏 朚 朚蜿畻 䥭頫䫊 䥭幟 螞㳯 䥭醧抢 䄕靈 㨑藿餈</p><p>柊䉮蘖乨蜿 祦侪留 㺲䯼柔 汋堣尙 輛頫黥 䥭旊鸀 馋鳜鸀 犳盔㫚</p>";
		//htmlCode = "晁㼁䟖䏪瓤䎣䖠㨋縆劊棃㗃噽艅撷㻸跘䦭狦裖侜椃</p><p>拂 其窩交䣲棃蘬璼劊㵫殧鹭㐎䪣刍魛<span class=\"big02\">甙</span>疧峞觯剺笙<span class=\"big02\">㨐</span>秂䃲妻䆯幸膆盭臅嗉忇蓃鍵㰭嘬寂鼧鸤庛</p><p>拂游藝尟脆漵妤㼎嘲猩㜧猩莇粓鐛剞馐㵔掄鱣㿡䭲毆遥芹</p><p>拂 欈魌綵懳縆㺥㬿瓾脆苤㳙掄誒奼寏碊菟擷純脽椃昀标䝶</p>";
		//htmlCode = "我们看看</p><p>测试下我们看看";
		//htmlCode = "<div class='biaoti1'>捠　怌鼓褉鿙</div><p>齘<span class=\"zhu\">䒃虒</span>㵦聆</p><p>重瀉勷倀呦噧</p><p>阷棶咕淞</p><p>䩂䢦衷䋰潳</p><p>捠䠍䤜咕涫癚貪翏䫁</p><p>矍倫兴案堖䫁</p><p>梷娉䒃虒蠊佊蠊佊诗湏诗湏閊傹㘁咕䶂膔宁</p><p>棃怌咕</p>";
		//htmlCode = "<div class=\"biaoti1\">栍隈㼩昼</div><p><span class=\"luokuan\">（蕌㴬朋䥷</span></p><p>【馫】<span class=\"zhu\">衙侧碬欛䗘黍孪苨鼝㵝㡸<span class=\"big02\">逐</span>筩阾䌢鿹讱鶇";
		//htmlCode = "<p>㜗攏枽<span class=\"zhu\">葭淧细害㽿</span></p><p>㾯礜枽<span class=\"zhu\">葭淧细害㽿</span></p><p>锫枽<span class=\"zhu\">葭细害㽿䦴枽榱城簿極熟䕵粤闡槤眅㰋猢梍</span></p><p>䵯枽<span class=\"zhu\">葭䰚细㽿苖枽彧爂㾿簿䵯枽攏筍政葭榱䵯枽冼䜼鲥烲簿䋥䵯枽恕</span></p><p>苖枽<span class=\"zhu\">葭䰚细嬚㽿怾䰚细脴粌淧细㽿䉻軨枽㾿簿皔追拙鬣賢冈簿燊舋恕㵅氦㙚虌嬤獝䦏滠峈峎斘䟵鞐䳬䦒鏬凋胇俶㾿㩾搶䦒<span class=\"big02\">眆</span>䳬追拙冡韹餑</span></p><p>㷳㞁枽<span class=\"zhu\">葭榱纨细盠㽿怾细淧脴䂱<span class=\"big02\">鳙</span>盠㽿</span></p><p>豑钻枽<span class=\"zhu\">葭榱纨细淧㽿怾淧细害脴粌嬚㽿纨㾯䖈纨追砲賢枽嗘䦏</span></p><p>䙒卡枽<span class=\"zhu\">葭害细㽿砲賢砲棟䙒卡胇枽錁熁</span></p>";
		
		//htmlCode = "<div class=\"biaoti1\">推掣跽矿䩤褷敖哤麌</div><p>褷昧濽㓓洳魏測<span class=\"big02\">欨</span>㒩彠佸澷䣙攴㾺牵哤㭂慂䂖笶邐琭哤㖥侐駖笶硷赃蓟鍫酓篶屢筞䢗㓓䁹枃㖖㷐詥临澋恰䥻㕵澋䦛鼢䎐寲䐊績㓓嬕终㶙哤昧測杘䥻濽聂遨㓓姁濽䟳哤募硷㞔㓓䒌昧赓笶躯昧濽褷</p><p>貇昧䂖恰稑㭰恹詍瞊锆憴鼢䦠栓麌仉䃎鼢酺鼂羶谙仉玧針鼂㒩颅㕒昧<span class=\"zhu\">稑㜋㾺杘褷㯼栻菐褷䝱昧邐颅㕒昧㷐薼哤㒩㯜坆硷薃</span>鼂㒩䝱昧<span class=\"zhu\">稑㜋褷饝遨㜽崵䝱䃶䝹㳶嫱櫃澋坆夢䃶䝹鍶䝱憏熖昧儲䝱昧㻥戬涡槶褷饝遨㜽㘉鈸貇㯜熖儲䝱昧皐睜髣绍䩙褷䮂族哤㘉㒩貇樜䂖饝㾺䟴瓫屢遨㜽㘉㷐</span>鼂㒩㨐颅昧<span class=\"zhu\">涡槶褷罱錓儼㽒噫㵮䣔稑鴚㜽晱跽哤疤塤尥㓓䣔䝱昧恰䐊㨐尥崵崵全恬圶㯬颅㕒逎㻤儼㽒噫䣔崵䐊㨐尥㒩㨐颅昧皐涡槶褷吔㯼罱噫硷义㖖昧㒩颅㨐颅嗶瀅逎竖茳嬠漸碌劥唴鍶坆寲罱噫洳漸劥鈁鱃恬攴</span>鼂㒩嫱亞昧<span class=\"zhu\">䩛勚䩤褷䙸齁匛蔗熖</span>昧㼜膌鼢恹儲且酺膌<span class=\"zhu\">涡槶褷茳恹酺";
		htmlCode = "䡗</span>軨攔㾁剢蟈<span class=\"zhu\">豻㡡壽賠䧳鏨褆頢烻渪蓞蘾艉耊譙枦锋硐䟏怿䙈剢蟈<span class=\"着重号\">豣</span>䕏计桥犣䛳蠋壄轏闦嶇</span>軨嶌㾁豻愃㾁䗴㕲<span class=\"zhu\">䟀晰蠕珯㽎</span>㾁皇墶<span class=\"zhu\">鬪掜㽎䢽鋖䉝豻蠋䵇舛咶舦柛䌸褆唝嶌㴓餮撫䝅橀灙鴻车砘䙔鶶䧜攷縭幻皇墶狷数皇墶嶌</span>軨车㾁雥鮍㾁兟飪<span class=\"zhu\">晰蠕珯㽎</span>㾁烧车<span class=\"zhu\">鬪掜㽎㴓䌸褆熂洢䢽来灙蔦轏烧䞕狷慜禛数</span>軨䟒㾁僖麂㾁皇黁<span class=\"zhu\">晰蠕珯㽎㲶亀㽋㰬霱慫踻䤑迏岘</span>㾁旌冃<span class=\"zhu\">軪唼㫣㽎鏨褆熂㡡沃䉝淴轏旌冃鏮漬皇黁㣀㯭酽鸈旌冃㨁䛀</span>軨鱤㾁棊唼<span class=\"zhu\">供湀㵃鿒嗏䍹峏曀㰬䌢唼㴓䌸褆熂㹙睖㾁棊唼蘾唏愮豣烹頟刕㞁蕗<span class=\"着重号\">玫</span>㨁棊唼䦼淀㲶䛀䘕熂硐䮓㷺㰬灁鳜㨩菫堢鈂臙㨁棊唼鱤䒊繵禛飕碫眍</span>㾁䢙庇<span class=\"zhu\">熂硐姤竚徤薶㟌㜩泼粖鎋䌸褆竚徤㨁䢙庇鱤㸷清㞁泼宽䴭丣㣴</span>㾁熂鱤<span class=\"zhu\">亀䎊觅䵪薶棂渳覭㨁䌸褆熂鱤㳺䵪蠋鈽聪䌢哫曀㷴</span>㾁核檬<span class=\"zhu\">䟀晰蠕珯㽎䘕鮯唃烣椦磹鵾犣瘵磹轏鱤飕軨数畗䤱厅</span>軨橀㾁餮撫䝅橀<span class=\"zhu\">鬪掜㽎橀㴓毗䟌鈾灙晰蠕珯㽎爹淴㽎勡滌舛蝎䮕豣娀孤淢㶵湵慀鶖䏑䤱䐣餮餭汎帻勡祃㷺砘虇来酜㹙淴㭠霍阁栚来酜濷哚褆桥㠻扏㽋䝅砘橀濰<span class=\"着重号\">䤱䠆</span>摽觡慫烹跭<span class=\"着重号\">棐</span>鞧鵾崫氖</span>㾁酜琹<span class=\"zhu\">㥝碗顬髓岱物岘黁㞧䦈浺鳷唝䏊䦈轫椈巩䓅舦䪵㢖㨁䌸褆譙来狷㣄示漬鍛来酜琹轏糜嶇唝㽎</span>㾁䀕琹<span class=\"zhu\">鬪掜㽎鏨㰬䕝奈況褆琹㨩匶䇴尼䌸褆脮䋀轀鿾嶍䀕琹煰懅鐑䞕塨䘕鮯鏨㰬䕝禚煰䏊鑙䙈㲶䀕琹撀</span>㾁滌攷湔<span class=\"zhu\">鬪掜㽎哫瞨䡗欅铪轏滌攷湔靶鶶滌峅畗睅唝䤱濰</span>㾁皇";
		htmlCode = "奕膕柧洴詣車㭙驥掷䦃镮洽才䫖虘篸凨渼镮鸄妇肋夜駢紨需泱䦃壀車驥冠镮岕樚绔㸘诧㭙夜脾膕柧洴詣車鏘溑苖教掷饐渼嘆妇官驥才屨滧屨堝㭙㓩掷需渼嘆妇碭㻷䦃肉彠咟绔䔨臿㭙緝奕柧洴筲蔰蛒猟車驥逾螸椭㙲螸鷏蝟賭㖅䄈㙲矗崙㭙蝟緝奕膕夜䦃煙䏴餭嫾椭瑖譫褧掷需㭙棫學镝瓆膕㭙洽溆學镝瓆膕鸄溆賭㖅䄈墦䆾㟫㭙柧苖䦃煙镝洽溆鏘緝奕膕夜峊鐣䦃倎镝鸄溆鏘镮詣掷需㔖鸄溆棫教㮞缝渼緝奕镮殃彉鐣匒蕣膕䟖蠋鸸㒧绔㸘诧䮈䦝䰳咟疎绔㭙緝奕棖齣膕䟖镮夜䁶溑䂿泱鑒齣峊鐣疎嘆諼㭙㒽嫾緝奕䁶屨扙镮㸩教詣媅晋镮勳鎽麠難䆾㟫鱹檠臿㭙隀</span></p><p>㮞觵颗膕㒧箤椭䴭筲劤栽<span class=\"zhu\">䞏忸槐肏紨绳垥㮞觵颗渼嘆膕茜䦃才㸩怶洽鍇肏紨绳桼㮞䞏炚續绳玤㮞觵颗兼需凨肏鬖洽忸䞃㜙貯䳼䃢蚫㮞䞏炚暙續䇧滧瀼惬玤㮞觵颗茜㮞兼需绊烆軆㴶莓檭絘蒅炚䛕亐渼鎽需車烆軆肏㧮檭槵枑䄖哀櫫渼夯絘鏹瀼惬嘶椭需劤豺㓩崇缸䦃颗茜玤䫖鳕篸杺嚄牞㭙歨譑贋玤㮞觵颗罄膖辀匬髺麠谝嘆膕柧洴詣車墦绔車鸄屨㑙繤䔨䛁㭙膕苖绔㪭风禍鼯屨㑙繤膕</span></p><p>㥣䭥膕㒧箤椭鯣彵劤栽<span class=\"zhu\">汉纳劾怆噺龢醭䙏鈣㜙覗䞏㥣䭥渼嘆膕茜䦃才㸩</span>㪭鼯鸄昉膕<span class=\"zhu\">怶绔㪭凨䇉才稌鉆膕</span>齣鸄昉穵齣榲鈦穵<span class=\"zhu\">䉚槐葘㸩京鈦</span>齣㵄㘾䂿<span class=\"zhu\">怶諼䁶凛䖜</span>齣䯩馉阐齣駢樥䈼<span class=\"zhu\">欄䄰䊼槐</span>镮鈣眘齣鍘㛽膕<span class=\"zhu\">鑨婀䭥槐䡙䥆鍘琙㘾羯䦃<span class=\"zhuozhonghao\">軩</span>㛽绔㸩怶鍘";
		htmlCode = "<span class=\"big02\">猒</span>驛㘴<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span></span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"zhu\"><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>眯嗶慆亡鈳絤杖驣<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>泰<span class=\"超大字1\">□</span>䳛穋痺<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>䈖挘<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>　　<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span></span>眯澆<span class=\"zhu\"><span class=\"超大字1\">□</span>㔬<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>䞃慭<span class=\"超大字1\">□</span>背痺<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>　<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>㘲芎佊窙<span class=\"超大字1\">□</span>戓雰澆</span>保鈞澆<span class=\"zhu\">氏㔬鹐　<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>揖胚痺<span class=\"超大字1\">□</span>戓踱陪奃䯐挘锇<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>䟛供<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>摪</span>汮㞋澆<span class=\"zhu\">氏㔬鷖泰㓪螇慭揖<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>傷蒹䯐挘<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span></span>鷖<span class=\"超大字1\">□</span>澆<span class=\"zhu\">氏㔬鷖骉<span class=\"超大字1\">□</span>跆薚揖胚欰泰輮圩䒷狓<span class=\"超大字1\">□</span>㖅䯐挘䃆芋</span>聘壻澆<span class=\"zhu\">氏洸<span class=\"超大字1\">□</span><span class=\"超大字1\">□</span>镎鹦䶊絩伅背孟輮访衢痺驛䖏祡芎<span class=\"超大字1\">□</span>隇";
		htmlCode="<p>下叙和滁<img src=\"http://njxadmin.njcbs.cn/huanghe/wapi/common/download?filename=/dfb52b0f92e5432aba623bd779ddf51e/zt/Z-ZSK75499-000038-L00022-4-7.png\" class=\"zitu\"></img>此</p>";
		htmlCode="<div class=\"biaoti1\">應天府志序</div>续写<p>應天府</p><p>髙皇帝故都在焉文獻甲扵天下而府未有專志非所以示官方翊</p><p>皇極也仰惟</p><p>髙皇帝統一寰區實始事扵玆域<span class=\"meipi\"><span class=\"楷体\">本序原本闕，現據中國書店版《稀見中國地方志匯刊》影印本校補。</span></span>";
//		htmlCode = "血㢍縞䋁䴨褉剧踦菜㔖竪䖫㪓隵跖叫䝲灏突䵹檆䳃䦦紂飍㸎濍狾㪓䩯袖</p><p>又㜒嘊䳃崓竪獙褆末㜚鯱譞釘婈禓褆趗嘊䵊吇淗柿厸厸竪跖䐀紋䝲又末㬱䥎洝睴鄬砼<p><span class=\"kaiti\">䴹碲鸐䴹揫，絧杁㙴䥋瓜踜腦《驈桺㙴䥋幼蠀繛胹鮾》墣䘽䴹嶴齢。</span></p></p>";
//		htmlCode = "<a class=\"page\" id=\"ZSK12446-000061-L00045\" onclick=\"openReader('ZSK12446-000061-L00045')\">P45</a><div class=\"mlbt1\">䝼埇劭鄴焬</div><div class=\"mlbt1\">堞軲轢</div><div class=\"mlbt2\">煷寫鲯<span class=\"zhu\">謻濝坋鲯乧</span></div><div class=\"mlbt3\">䝼埇眚轚镎爧鄴鲯</div><div class=\"mlbt3\">醏䭭䴿媤奰寄鈂忰鲯</div><div class=\"mlbt3\">甆䭭㢆趕藙晨鲯<span class=\"zhu\">鲯䔼㺸謻</span></div><div class=\"mlbt3\">凞拦瑝抩䫏厜䥴鲯</div><div class=\"mlbt3\">脉倀褦媤鲯<span class=\"zhu\">埚䘩豲坋</span></div><div class=\"mlbt3\">㒈榪褦翝皰抩魉揉煷宥鲯</div><a class=\"page\" id=\"ZSK12446-000062-L00046\" onclick=\"openReader('ZSK12446-000062-L00046')\">P46</a><div class=\"mlbt3\">艧㠽骵鲯</div><div class=\"mlbt3\">閹鎡骵鲯</div><div class=\"mlbt3\">鲺窗骵鲯</div><div class=\"mlbt3\">罚祇杄鲯</div><div class=\"mlbt3\">罚嗦杄鲯</div><div class=\"mlbt3\">褦姏劭鲯</div><div class=\"mlbt3\">䭭䫏醇㐠鲯</div><div class=\"mlbt3\">媤䫏鲯</div><div class=\"mlbt3\">逪眚鲯</div><a class=\"page\" id=\"ZSK12446-000063-L00047\" onclick=\"openReader('ZSK12446-000063-L00047')\">P47</a><div class=\"mlbt3\">㻹眚鲯</div><div class=\"mlbt3\">荱鑶蛛脉倀禕鲯</div><div class=\"mlbt3\">莡醏稡器欎鲯</div><div class=\"mlbt1\">堞䔼轢</div><div class=\"mlbt2\">䝼埇戗丗</div><div class=\"mlbt1\">堞奰轢<span class=\"zhu\">閹䅓䓽</span></div><div class=\"mlbt2\">䝼埇霣荰㝹</div><div class=\"mlbt3\">栾㵻鎡稡㴕荰齰䎰捯忊乧頉悈礳㱇㟴霣荰㝹㹒轢噛閹</div><a class=\"page\" id=\"ZSK12446-000064-L00048\" onclick=\"openReader('ZSK12446-000064-L00048')\">P48</a><div class=\"mlbt3\">栾湨膃貆齰㟴捯硦㞰傇㿃䱱霣荰㝹㹒轢噛䅓</div><div class=\"mlbt3\">栾捯鎡鈂奰荰軥鱖炫㸌荰㝹㹒轢噛䓽</div><div class=\"mlbt1\">堞㴕轢</div><div class=\"mlbt2\">箻爧<span class=\"big02-delete\">矮</span><span class=\"zhu\">剚脪　嫗爴　煷㹒榪　煷㹒媤躃　煷躃鴲䱾聕　煷躃餩杄聕　煷躃餩伲聕　煷躃诞衎伲聕　煷躃诞抩㗸　煷躃脳骵聕杄聕　剚脪哹骵聕　煷躃䦆㴕放　魉咧　弪䶘　纉㷿　橖猯　忰褦　衎㫝　坙钰　餋钒　瑗嬨</span></div><a class=\"page\" id=\"ZSK12446-000065-L00049\" onclick=\"openReader('ZSK12446-000065-L00049')\">P49</a><div class=\"mlbt1\">堞茠轢</div><div class=\"mlbt2\">眚轚䋈<span class=\"zhu\">眚䄋　蹱埝　艧抈　娎㪭　澊㭹　枰䮸　鸼牉　氎煀　艤祇　趮㱚　煎㘕</span></div><div class=\"mlbt1\">堞䇀轢</div><div class=\"mlbt2\">䦜揉䋈<span class=\"zhu\">懹脪䦜㴸　樞㟵脳餩䦜㴸　懨聕</span></div><div class=\"mlbt1\">堞橘轢</div><div class=\"mlbt2\">䫺吒䋈<span class=\"zhu\">懹脪嫗庋　樞㟵䫺弜　愉吒　锳㙙</span></div><div class=\"mlbt1\">堞噡轢</div><div class=\"mlbt2\">皔懗䋈<span class=\"zhu\">醇焱㮬鴆　蓢懗</span></div><a class=\"page\" id=\"ZSK12446-000066-L00050\" onclick=\"openReader('ZSK12446-000066-L00050')\">P50</a><div class=\"mlbt1\">堞懩轢</div><div class=\"mlbt2\">姏睺䋈<span class=\"zhu\">懹脪姏㴸帺㟆　诞徚咴　㧫姏莉　刿岶鸈　挧撃　拦眩趕　诞骵姏　䉔䮑旺　懹脪愉㐭　愉㟕　愉趕　樞㟵姏睺　拦挧眩趕　杄骵姏　昩堞絩㐭　琑咴</span></div><div class=\"mlbt1\">堞鈂轢</div><div class=\"mlbt2\">嶋鸢䋈<span class=\"zhu\">懹脪厜䥴　坈揉　艧鸢　䨯孰㗬翪　鬽咴　䯺<span class=\"big02-delete\">瞓</span>　闊昲　樞㟵嶋莼</span></div><div class=\"mlbt1\">堞鈂軲轢</div><div class=\"mlbt2\">器䉔䋈<span class=\"zhu\">醇墴軾　㷒崬　器軾　撃搜　禕趕</span></div><a class=\"page\" id=\"ZSK12446-000067-L00051\" onclick=\"openReader('ZSK12446-000067-L00051')\">P51</a><div class=\"mlbt1\">堞鈂䔼轢</div><div class=\"mlbt2\">醇㐠䋈<span class=\"zhu\">䫏<span class=\"big02-delete\">忈</span>　䦜晨　㣘䩿　埇拾　<span class=\"big02-delete\">㫀</span>箰</span></div><div class=\"mlbt1\">堞鈂奰轢<span class=\"zhu\">閹䓽</span></div><div class=\"mlbt2\">寑锳䋈</div><div class=\"mlbt3\">霣轛<span class=\"zhu\">伲嬮　㺠泄　镎㡭</span></div><div class=\"mlbt3\">凾鍘<span class=\"zhu\">柟䚮　㷩岶　㹪圯　媤甆　琑矐　罵<span class=\"big02-delete\">鏍</span>　谽凞　岨准　㞅篏　凾考</span></div><div class=\"mlbt1\">堞鈂㴕轢</div><div class=\"mlbt2\">䴜銾<span class=\"zhu\">㼘崷拦瑝蒄埚艤䄒鍘褽䴭<span class=\"big02-delete\">散</span>炫䓽被醏錡勈硦鲒<img src=\"http://114.255.120.44/huanghe/wapi/common/download?filename=/45eb2a8587c0428fbbcc02eefeea4f18/zt/Z-ZSK12446-000067-L00051-9-16.png\" class=\"zitu\"></img>艧䗀銾扛<span class=\"big02-delete\">鏍</span>䘩</span></div><a class=\"page\" id=\"ZSK12446-000068-L00052\" onclick=\"openReader('ZSK12446-000068-L00052')\">P52</a><div class=\"mlbt1\">堞鈂茠轢</div><div class=\"mlbt2\">㯯鎣<span class=\"zhu\">艤䱾㯯　龫䥉　鎣謻</span></div>";
//		htmlCode = "<div class=\"biaoti1\">㧦抟竔韌䲌赗㖅嫟頊㩀没<span class=\"luokuan\">䐾乔陰㪷䇇抟</span></div><div class=\"biaoti2\">趘䱻<span class=\"zhu\">靮蛵</span></div><p>䗀瓑秔㷂靮气㩀斴駒鰷鋽裔㷂㻀撑翥肈鄕樑鄹翥披鄹攷㩀翥䱡誐挑嵱鞞瓥㞥䄃䬆䴀㩀萡㹒栿痴魨婀䥂鄹璃檖誐䋢嘙然覞槽捀琾瓎蔾㜌礪袞斌㘌毓㩀䝻弒賅煺㩀闔瓬㬘䵼㩀楑楫瞿䎴㩀唣崅懶㩭靮祶旜㩀挶䈂㩀鄹㠄䗩朜㐕秔挑篃樑<span class=\"big02\">病</span>罩趘㷂靮㩀䁨㶾萡渔䨣靮䅛翥攂鄹綎诘䈂嘂宮鶫菢霮鿪熆趘琾翥肈䉉㜌鶁䶿㩀祗綎慇㧦靮蛵赗覞熵锗宮椷鄕乌奵鎉气槽增㩀葟綎</p><p>鶫覹<span class=\"zhu\">䬆䨚　懹靮㒛　椷㮭遷</span>　幼驊䀅</p><p>樁镓弗　瞿蕹</p><p>跤邧<span class=\"zhu\">㒭䜳㾬　醺呞气　舤犇壤</span>　痴撘驊<span class=\"zhu\">蚈礁　痴跤蜨</span></p>";
//		htmlCode = "輿地者無慮數十家隻詞少<img src=\"http://114.255.120.44/huanghe/wapi/common/download?filename=/24f4bdc8ee51437f872189ffae3fc9a7/zt/Z-ZSK75496-000048-L00032-1-12.png\" class=\"zitu\"></img>君子猶敝帚視之乃金何人斯敢以蚊負耶余謂山人曰不穀慮簿書弗給以故欲得分任於君君職其詳余職其要惡乎不可且也千金之裘不成於一狐之腋君無讓矣山人唯唯退而屏居蕭寺中博集羣書肆力叅訂歴數月草乃具余復爲之拾其遺汰其冗<span class=\"big02\">&#x28932;</span>其譌間以評品附焉凡浹歲而後報成事卽不敢妄意有加於前顧漏也複也<span class=\"big02\">&#x27e1b;</span>也或庶幾哉免夫誌旣成復進山人與語曰君知郡邑有誌卽國有史乎史以備一代彰癉誌以備一邑勸懲爲功";
//		htmlCode = "<div class=\"biaoti1\">嗜嵆眅癒旯葑猡覕告㤂丹<span class=\"luokuan\">䩤斃䳶瓃裊嵆</span></div><div class=\"biaoti2\">贲㣰<span class=\"zhu\">䃂橆</span></div><p>炱阉吓杘䃂鍳㤂䀄奕笛拾溿杘㥶錌㒆郆受铨㨧㒆攂㨧缛㤂㒆钴嶿趏岑裖罧冫䰻詡閌㤂瓊譻杮鲭䓆桙冥㨧岳褳嶿孳仛編凑㺂䗕䮕䘚綄礫總埼钌蹈蝯㤂垡㰢媎奌㤂䉩噘㢖徖㤂㻹婪箕䑰㤂偭䴑諚骸䃂躩杤㤂㝳埽㤂㨧闍剫觰䐟吓趏曆铨<span class=\"big02\">䥂</span>鸿贲杘䃂㤂撂漁瓊㯧莶䃂烮㒆礻㨧䂠挰埽䀇嵵㨌奎奣踃毄㫟贲䮕㒆郆䝍礫篂羭㤂畚䂠䘖嗜䃂橆葑凑㽩旝嵵消受眒臹洧鍳㺂稿㤂夶䂠</p><p>㨌䙎<span class=\"zhu\">詡詅　㕈䃂慌　消羡硢</span>　珐扣㟀</p><p>衠䜑硋　箕汭</p><p>鬉橪<span class=\"zhu\">㮻睰䚙　莊玡鍳　䆊啠㑹</span>　鲭閐扣<span class=\"zhu\">莼櫻　鲭鬉得</span></p>";
//		//htmlCode = "壶。</span>弼镍熪鹝盺。<span class=\"zhuanming\">鬻耓</span>㕲䂡弼<span class=\"着重号\">摍倚鷢挅罯葽溸疬。</span><span class=\"shuming\"><img src=\"http://114.255.120.44/huanghe/wapi/common/download?filename=/085eb98ebc2a4bd2baed97d889ac4816/zt/Z-ZSK92243-000217-L00299-1-20.png\" class=\"zitu\"></img>猾</span><span class=\"zhuanming\">鍬籃</span>帀盺倚螳。睝昖。縰棺䂡<span class=\"big02\">䙫</span>蜩䞥。嵡䉫。䘝圝䱫昖䞎。鎝补愅、垬䣠䞷㣐氳。瑋劭弼隝颫愅。瑋㘲勡臷。帀㯥䞿㰤姙䇀䞎。棺䂡㣐淫䱫㯥。著。粀<span class=\"zhuanming\">㞗</span>棺䂡。籂狩艛酊帤。㯥䗭帠氳。鳧騝罯雗蜩、<span class=\"着重号1\">䏸狶氳兵。磓闚煞鹝始囁。櫵髷儽髑、</span>䂡塇䆭氳。䙋膸免<span class=\"zhuanming\">柚瞌每。</span>䃯鷢<span class=\"shuming\">雗彔</span>姣䞎倚。夁曬燁著。<span class=\"zhuanming\">㞗</span>棺䂡圝愅昖。㣮篬巿<span class=\"zhuanming\">榢軚睤。</span>弼氳醖著。傇傇幯恑。㼈㼈鿄昖。龾<span class=\"big02\">鍕</span>窽䢏。㦳寺复霐。䋎偹<span class=\"big02\">坆</span>㣐。㕥夁䛫䀩。<span class=\"big02\">滌</span>芅砽亃。瑛骭絙瓎。寺镩姣䞎。帀㸜㹮桫。㦧赨输㞿。䞥暥祼瘹。侸厕䢏支。偹鏀柾";
//		htmlCode = "<div class=\"biaoti1\">黏鬐趭珃膏抺䋅<span class=\"luokuan\">紂繢蚙篥䜊蚤皍　鳶篦繢蚙鎺鐺噽陭　㸮聬繢蚙悴䧧噽陭</span></div><div class=\"biaoti2\">販答窫</div><p><span class=\"zhu\">販羈鳩缇答羈鶉缇<span class=\"big02\">榊</span>臑羈攩嗱触璚椸疀㚮匧楲䉲戊触饷遤坤鏍栴礑饷唴䪳抺器販器殻遤唴蔄触茹抺灏器䥐恞㘙㥇魴腱㱡販答窫</span></p><div class=\"biaoti3\">販<span class=\"zhu\">殻苤䬧蕺鼯䉲戊㬠怓缇謾缇唴礋</span></div><div class=\"biaoti4\">趭</div><p>㞯晵鮳殻<span class=\"zhu\">鋡豱閥㚿朁嵃䨘饕朁㬠<span class=\"big02\">䥪</span>䜀㡊㬠捇㧺眆嵃䨘淗圬<span class=\"big02\">䥪</span>丮礅䩸孨囌诜䲂线欐捇䗠眆䁤㥇";
		
		System.out.println(htmlCode);
		htmlCode = StringUtil.replaceAll(htmlCode, "<img src='[a-zA-Z0-9\\:\\/=\\?\\-\\.]*'  class=\"chatu\">","");
    	
		//htmlCode = StringUtil.replaceAll(htmlCode, "div", "span");
		//System.out.println(htmlCode);
		
//		Map<String,String> result = CompleteTagsUtils.subStringHTMLTagSpan(htmlCode, htmlCode.length(), "");
//		htmlCode = result.get("DATA");
//		System.out.println("DATA=>"+htmlCode);
				
//		htmlCode = CompleteTagsUtils.subStringHTMLTagP(htmlCode, htmlCode.length(), "");
//		System.out.println("subStringHTMLTagP=>"+htmlCode);
//		
//		htmlCode= Jsoup.parse(htmlCode).body().html();
//		//对字图特殊处理
//		if(StringUtil.contains(htmlCode, "class=\"zitu\">")||StringUtil.contains(htmlCode, "class=\"waizi\">")) {
//			htmlCode = StringUtil.replace(htmlCode, "class=\"zitu\">", "class=\"zitu\"/>");
//			htmlCode = StringUtil.replace(htmlCode, "class=\"waizi\">", "class=\"waizi\"/>");
//		}
//		htmlCode = TransferUtils.getResult(htmlCode);
//		System.out.println("格式化后==>" + htmlCode);

	}

//	public static Map<String,String> subStringHTMLTagSpan(String param, int length, String end) {
//
//		Map<String,String> result = Maps.newHashMap();
//		// 取出截取字符串中的HTML标记
//		String temp_result = param;
//		//log.info(temp_result);
//		// 去掉成对的HTML标签
//		// temp_result = temp_result.replaceAll("<([a-zA-Z]+)[^<>]*>(.*?)</\\1>", "$2");
//		temp_result = StringUtil.replace(temp_result, "<span class=\"着重号\">", "<span class=\"zhuozhonghao\">");
//		temp_result = StringUtil.replace(temp_result, "<span class=\"着重号1\">", "<span class=\"zhuozhonghao1\">");
//		temp_result = StringUtil.replace(temp_result, "<span class=\"超大字1\">", "<span class=\"big01\">");
//		//temp_result = StringUtil.replace(temp_result, "<span class=\"楷体\">", "<span class=\"kaiti\">");
//		temp_result = StringUtil.replace(temp_result, "<span class=\"楷体\">", "");
//		
////		if(StringUtil.indexOf(temp_result, "<span class=\"meipi\">")!=-1) {
////			temp_result = StringUtil.replace(temp_result, "<span class=\"meipi\">", "<p>");
////			
////			int last = StringUtil.lastIndexOf(temp_result,"</span>");
////			//String tempResult = "";
////			String t1 = temp_result.substring(0, last);
////			String t2 = temp_result.substring(last+7, temp_result.length());
////			temp_result = t1 + "</p>" + t2;
////		}
//		System.out.println(temp_result);
//		//df
////		int idx = 0;
////		Map<String,String> map1 = replaceMlbt(temp_result,"<div class=\"mlbt1\">",idx);
////		temp_result = map1.get("text");
////		idx = Integer.parseInt(map1.get("idx"));
////		
////		Map<String,String> map2 = replaceMlbt(temp_result,"<div class=\"mlbt2\">",idx);
////		temp_result = map2.get("text");
////		idx = Integer.parseInt(map2.get("idx"));
////		
////		Map<String,String> map3 = replaceMlbt(temp_result,"<div class=\"mlbt3\">",idx);
////		temp_result = map3.get("text");
////		idx = Integer.parseInt(map3.get("idx"));
//		
//		temp_result = StringUtil.replace(temp_result, "<div class=\"mlbt1\">", "<p>");
//		temp_result = StringUtil.replace(temp_result, "<div class=\"mlbt2\">", "<p>");
//		temp_result = StringUtil.replace(temp_result, "<div class=\"mlbt3\">", "<p>");
//		temp_result = StringUtil.replace(temp_result, "</div>", "</p>");
//		
//		temp_result = StringUtil.replaceAll(temp_result, "<div class=\"biaoti[\\d]+\">", "<p>");
//		
//		//log.info("原始字符串==>" + temp_result);
//
//		StringBuilder buf = new StringBuilder(temp_result.length());
//		// 用正则表达式取出标签 只要是”^”这个字符是在中括号”[]”中被使用的话就是表示字符类的否定，如果不是的话就是表示限定开头。
//		buf.append(temp_result);
//
//		Pattern p = Pattern.compile("<span class=\"[a-zA-Z0-9]*\"[^<>]*>|</span>");
//		Matcher m = p.matcher(temp_result);
//		//int index = 0;
//		int preStart = 0;
//		String preTag = null;
//		LinkedHashMap<Integer, String> errorMap = Maps.newLinkedHashMap();
//		// 先找出不配对得标签，记录位置下表
//		while (m.find()) {
//			String tag = m.group();
//			// String tag1 = m.group(1);
//			int start = m.start();
//			// log.info(tag1);
//			//log.info("==>" + tag + "->" + start);
//
//			preStart = start;
//			preTag = tag;
//			//index++;
//			errorMap.put(preStart, preTag);			
//		}
//		//log.info("==>"+errorMap);
//
//		LinkedHashMap<Integer, String>  _result = errorMap;
//		for(int i = 0 ; i < 3; i++) {
//			_result = getMap(_result) ;
//		}
//		result.put("TAG", "NORMAL");
//		log.info("==>不配对结果："+_result);
//		for(Entry<Integer, String> entry : _result.entrySet()) {
//			int start = entry.getKey();
//			String tag = entry.getValue();
//			log.info("==>" + tag + "->" + start);
//			
//			//_map.put(start, tag);
//			if(tag.contains("</span>")) {
//				
//				//需要查上一页数据
//				//buf.insert(0, "<span>");
//				result.put("TAG", "PREPAGE");		
//				result.put("DATA", buf.toString());		
//				//return result;
//				
//			}
//			if(tag.contains("<span")) {
//				buf.insert(buf.length(), "</span>");										
//			}
//		}
//		
//		result.put("DATA", buf.toString());		
//		return result;
//	}

//	public static Map<String,String> replaceMlbt(String temp_result,String querykey) {
//		Map<String,String> map = Maps.newHashMap();
//		StringBuffer ml1buf = new StringBuffer();
//		//String querykey = "<span class=\"mlbt1\">";
//		String[] qks = temp_result.split(querykey);
//		int idx = 0;
//		for(String qk : qks) {
//			idx++;
//			log.info(qk);
//			if(idx == 1) {
//				ml1buf.append(qk);
//				if(qk.lastIndexOf("<p>") < 0 ) {
//					ml1buf.append("<p>");
//				}
//				
//				
//			}else {
//				//ml1buf.append("<p>");
//			}
//			int lastidx = StringUtil.indexOf(qk, "</div>");
//			if(lastidx > 0) {
//				//
//				if(idx == qks.length) {
//					String cc = StringUtil.replaceOnce(qk, "</div>", "</p>");
//					ml1buf.append(cc);
//				}else {
//					String cc = StringUtil.replaceOnce(qk, "</div>", "</p><p>");
//					ml1buf.append(cc);
//				}
//			}
//			
//			//log.info(ml1buf.toString());
//		}
//		String result = ml1buf.toString();
//		//if(StringUtil.lastIndexOf(result, "<p>"))
//		//result = StringUtil.substring(result, 0,result.length()-3);
//		log.info("===================>"+result);
//		map.put("text", result);
//		map.put("idx", idx+"");
//		return map;
//	}
	
//	public static List<String> findLastSpanTag(String param) {
//		List<String> pagelist = Lists.newArrayList();
//		// 取出截取字符串中的HTML标记
//		String temp_result = param;
//		// 去掉成对的HTML标签
//		// temp_result = temp_result.replaceAll("<([a-zA-Z]+)[^<>]*>(.*?)</\\1>", "$2");
//		temp_result = StringUtil.replace(temp_result, "<span class=\"着重号\">", "<span class=\"zhuozhonghao\">");
//		//log.info("原始字符串==>" + temp_result);
//
//		StringBuilder buf = new StringBuilder(temp_result.length());
//		// 用正则表达式取出标签 只要是”^”这个字符是在中括号”[]”中被使用的话就是表示字符类的否定，如果不是的话就是表示限定开头。
//		buf.append(temp_result);
//
//		Pattern p = Pattern.compile("<span class=\"[a-zA-Z0-9]*\"[^<>]*>|</span>");
//		Matcher m = p.matcher(temp_result);
//		//String lastTag = null;			
//		// 先找出不配对得标签，记录位置下表
//		while (m.find()) {
//			String tag = m.group();
//			// String tag1 = m.group(1);
//			//int start = m.start();
//			// log.info(tag1);
//			//log.info("==>" + tag + "->" + start);
//
//			pagelist.add(tag);		
//		}
//		
//		return pagelist;
//	}
	
	
//	private static LinkedHashMap<Integer, String> getMap(LinkedHashMap<Integer, String> errorMap) {
//		LinkedHashMap<Integer, String> _map = (LinkedHashMap<Integer, String>)errorMap.clone();
//		for(Entry<Integer, String> entry : errorMap.entrySet()) {
//			int start = entry.getKey();
//			String tag = entry.getValue();
//			log.info("==>" + tag + "->" + start);
//			
//			//_map.put(start, tag);
//			if(tag.contains("</span>")) {
//				//向上找
//				int preidx = getData("pre",start,errorMap);
//				if(preidx != -1) {
//					String pretag = errorMap.get(preidx);
//					if(pretag.contains("<span")) {
//						log.info("==>有配对的标签：{}",pretag);
//						_map.remove(start);
//					}
//				}
//			}
//			if(tag.contains("<span")) {
//				//向下找
//				int nextidx = getData("next",start,errorMap);
//				if(nextidx != -1) {
//					String nexttag = errorMap.get(nextidx);
//					if(nexttag.contains("</span>")) {
//						log.info("==>有配对的标签：{}",nexttag);
//						_map.remove(start);
//					}
//				}
//			}		
//		}
//		return _map;
//	}
    
    /**
     * 获取标志
     * @param gettype pre-往上找 next-向下找
     * @param start
     * @param errorMap
     * @return
     */
//	private static int getData(String gettype, int start, LinkedHashMap<Integer, String> errorMap) {
//		if(gettype.equals("pre")) {
//			int result = -1;
//			for (Entry<Integer, String> entry : errorMap.entrySet()) {
//				//String _tag = entry.getValue();
//				int _start = entry.getKey();
//	
//				if (start > _start) {
//					result = _start;
//				}
//			}
//			return result;
//		}else {
//			for (Entry<Integer, String> entry : errorMap.entrySet()) {
//				//String _tag = entry.getValue();
//				int _start = entry.getKey();
//	
//				if ( start < _start) {
//					return _start;
//				}
//			}
//		}
//		return -1;
//	}

//	public static String subStringHTMLTagP(String param, int length, String end) {
//
//		// 取出截取字符串中的HTML标记
//		String temp_result = param;
//		//log.info("==>处理P标签收到的原始：{}",temp_result);
//		StringBuilder buf = new StringBuilder(temp_result.length());
//
//		if (!StringUtil.startsWith(temp_result, "<p>")) {
//			temp_result = "<p>" + temp_result;
//		}
//		if (!StringUtil.endsWith(temp_result, "</p>")) {
//			temp_result = temp_result + "</p>";
//		}
//
//		// 用正则表达式取出标签 只要是”^”这个字符是在中括号”[]”中被使用的话就是表示字符类的否定，如果不是的话就是表示限定开头。
//		buf.append(temp_result);
//		// log.info("==>"+buf.toString());
//		Pattern p = Pattern.compile("<p[^<>]*>|</p>");
//		Matcher m = p.matcher(temp_result);
//		int index = 0;
//
//		// 先找出不配对得标签，记录位置下表
//		while (m.find()) {
//			String tag = m.group();
//			int start = m.start();
//			// log.info(tag+"->"+start);
//
//			if (!tag.equalsIgnoreCase("</p>") && index % 2 == 1) {
//				buf.insert(start, "</p>");
//				index--;
//			}
//			index++;
//		}
//
//		log.info("==>subStringHTMLTagP="+buf.toString());
//		String result = buf.toString();
//		int idx = StringUtil.lastIndexOf(result, "</p></p>");
//		if(idx != -1 ) {
//			result = StringUtil.removeEnd(result, "</p>");
//		}
//		return result;
//	}

}
