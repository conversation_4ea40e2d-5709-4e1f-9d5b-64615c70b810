package cn.guliandigital.common.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {

    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";
    /**
     * <AUTHOR>
     * @Description 上一张图片
     * @Date 2021/10/22 16:28
     * @param null:
     **/
    public static final String PREPIC = "P";
    /**
     * <AUTHOR>
     * @Description 下一张图片
     * @Date 2021/10/22 16:29
     * @param null:
     **/
    public static final String NEXTPIC = "N";
    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "huanghe:captcha_codes:";

    /**
     * 外网验证码
     */
    public static final String CAPTCHA_CODE_KEY_WAPI = "huanghe:captcha_codes:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "huanghe:login_tokens:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "huanghe:repeat_submit:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = "sub";

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    
    public static final int AUTH_LIMIT_WORDS  = 300;

    public static final String HTTP408 = "数据整理中，敬请期待...";

    public static final String NOAUTHUR_INFO_1 = "请<a style='text-decoration: underline;color:#4a90e2' href='";
    public static final String NOAUTHUR_INFO_2 = "/login'>登录</a>后查阅";

    public static final String NOAUTHUR_INFO_IMAGE = "请登录后查阅";
    public static final String NOAUTHUR_INFO = "请登录后查阅";

    
    public static final int COPY_LIMIT = 200;
    
    public static final int NOTE_LIMIT = 400;

    //章节复制上限
    public static final int COPY_LIMIT_CHAPTER = 2000;

    public static final int PIC_HEIGHT = 1200;

    public static final String COPY_LIMIT_MSG = "您本次的复制字数超出单次上限。";
    public static final String NOTE_LIMIT_MSG = "您本次的复制字数超出单次上限。";
    public static final String COPY_LIMIT_CHAPTER_MSG = "您今日的复制字数已达到上限。";
    public static final String COPY_LIMIT_CHAPTER_A_MSG = "您今日的复制字数已达到上限，本次复制内容不全。";
    public static final String CLASSEDNAME = "微信小程序";


    public static final int CHAPTER_LIMIT_WORDS = 50;
    //图片最大高宽
    public static final int PIC_MAX_HEIGHT = 524;
    public static final int PIC_MAX_WIDTH = 400;

    public static final String PINYIN_TEXT="本系统暂不支持拼音检索";
    
    
}
