package cn.guliandigital.common.utils;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.apache.commons.codec.binary.Hex;

import lombok.extern.slf4j.Slf4j;



@Slf4j
public class CheckSumUtil {
	
	
	/**
	 * 高效md5计算
	 * @param filepath
	 * @return
	 */
	public static String MD5(File file) {
		BufferedInputStream bufferedInput = null;
		String md5str = null;
		try {
			long s = System.currentTimeMillis();
			byte[] buffer = new byte[8 * 1024];
			// 创建BufferedInputStream 对象
			bufferedInput = new BufferedInputStream(new FileInputStream(file));
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			md5str = Hex.encodeHexString(hash(md5, bufferedInput, buffer.length));
			long costs = System.currentTimeMillis() - s;
			log.info(" MD5  耗时：" + costs + "," + md5str);
		} catch (NoSuchAlgorithmException e) {
			log.error("",e);
		} catch (FileNotFoundException e) {
			log.error("",e);
		} catch (Exception e) {
			log.error("",e);
		} finally {
			try {
				if (bufferedInput != null)
					bufferedInput.close();
			} catch (IOException e) {
				log.error("",e);
			}
		}
		return md5str;
	}

	private static byte[] hash(MessageDigest digest, BufferedInputStream in, int bufferSize) throws IOException {
		byte[] buffer = new byte[bufferSize];
		int sizeRead = -1;
		while ((sizeRead = in.read(buffer)) != -1) {
			digest.update(buffer, 0, sizeRead);
		}
		in.close();
		byte[] hash = null;
		hash = new byte[digest.getDigestLength()];
		hash = digest.digest();
		return hash;
	}
}
