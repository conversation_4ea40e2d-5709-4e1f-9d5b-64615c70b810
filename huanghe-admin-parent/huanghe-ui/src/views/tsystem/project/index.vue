<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="publishStatus" label-width="40px">
        <el-select
          v-model="queryParams.publishStatus"
          placeholder="请选择"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in publishStatusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:project:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:project:remove']"
          >删除</el-button
        >
      </el-col>
      <!-- <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar> -->
    </el-row>

    <el-table
      v-loading="loading"
      :data="projectList"
      @selection-change="handleSelectionChange"
      class="tableS"
      :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }" size='medium' :header-row-style="{ height: '20px' }"
          height="calc(100vh - 280px)" :header-cell-style="{ background: '#f5f7fa', padding: '0px' }"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column min-width="60" align="center" label="序号" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>      <el-table-column label="标题" align="center" prop="title" />
      <!-- <el-table-column label="类型" align="center" prop="contentType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.contentType == '0'"> 介绍 </el-tag>
          <el-tag v-if="scope.row.contentType == '1'"> 编委会 </el-tag>
          <el-tag v-if="scope.row.contentType == '2'"> 典籍篇 </el-tag>
          <el-tag v-if="scope.row.contentType == '3'"> 研究篇 </el-tag>
          <el-tag v-if="scope.row.contentType == '4'"> 史志篇 </el-tag>
          <el-tag v-if="scope.row.contentType == '5'"> 人物篇 </el-tag>
          <el-tag v-if="scope.row.contentType == '6'"> 文物篇 </el-tag>
          <el-tag v-if="scope.row.contentType == '7'"> 文学艺术篇 </el-tag>
          <el-tag v-if="scope.row.contentType == '8'"> 山水篇 </el-tag>
          <el-tag v-if="scope.row.contentType == '9'"> 科技篇 </el-tag>
          <el-tag v-if="scope.row.contentType == '10'"> 红色文献篇 </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column
        label="状态 "
        align="center"
        prop="publishStatus"
        sortable
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.publishStatus == '0'"> 未上架 </el-tag>
          <el-tag v-if="scope.row.publishStatus == '1'" :type="'success'">
            已上架
          </el-tag>
          <el-tag v-if="scope.row.publishStatus == '2'" :type="'warning'">
            已撤销
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="display" />


      <el-table-column label="创建时间" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.publishStatus === 0"
            type="text"
            icon="el-icon-top"
            @click="handlePublish(scope.row)"
            v-hasPermi="['system:project:up']"
            >上架</el-button
          >
          <!-- <el-button
            v-if="scope.row.publishStatus === 1"
            type="text"
            icon="el-icon-bottom"
            @click="handleDown(scope.row)"
            v-hasPermi="['system:project:down']"
            >下架</el-button
          > -->
          <el-button
            v-if="scope.row.publishStatus === 2"
            type="text"
            icon="el-icon-top"
            @click="handlePublish(scope.row)"
            >再次上架</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:project:edit']"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:project:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工程项目简介对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="标题" prop="title">
          <el-input
            maxlength="20"
            show-word-limit
            v-model="form.title"
            placeholder="请输入标题内容"
       
          />
        </el-form-item>

          <el-form-item label="排序" prop="display" :required="true" label-width="80px">
            <el-input-number style=" width: 100%;"
              v-model="form.display"
              controls-position="right"
              :min="0"
              :max="200000"
            ></el-input-number>
          </el-form-item>
        

        <!-- <el-form-item label="类型" prop="contentType">
          <el-select
            v-model="form.contentType"
            placeholder="类型"
            @change="changeTime"
          >
            <el-option label="介绍" value="0"></el-option>
            <el-option label="编委会" value="1"></el-option>
            <el-option label="典籍篇" value="2"></el-option>
            <el-option label="研究篇" value="3"></el-option>
            <el-option label="史志篇" value="4"></el-option>
            <el-option label="人物篇" value="5"></el-option>
            <el-option label="文物篇" value="6"></el-option>
            <el-option label="文学艺术篇" value="7"></el-option>
            <el-option label="山水篇" value="8"></el-option>
            <el-option label="科技篇" value="9"></el-option>
            <el-option label="红色文献篇" value="10"></el-option>
          </el-select>
        </el-form-item> -->

        <!-- <el-form-item label="图片" prop="iconUrl" style="margin-top: 30px">
          <el-upload
            ref="upload"
            action="#"
            list-type="picture-card"
            :http-request="requestUpload"
            :before-upload="beforeUpload"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :on-exceed="handleExceed"
            :on-change="handleChange"
            :limit="1"
            :file-list="fileList"
            :class="{ hide: hideUpload }"
          >
            <i class="el-icon-plus"></i>
            <div class="el-upload__tip" slot="tip">
              <span style="color: #f56c6c"
                >可以上传1张，建议尺寸640×640像素。</span
              >
            </div>
          </el-upload>
        </el-form-item> -->
        <el-form-item label="内容" prop="content">
          <Editor
            style="height: 280px;"
            v-model="form.content"
            maxlength="5000"
            show-word-limit
          />
        </el-form-item>

         <el-form-item label="状态" prop="publishStatus">
            <el-switch
              v-model="form.publishStatus"
              active-value=1
              inactive-value=0
              active-color="#13ce66"
              inactive-color="#000000"
            >
            </el-switch>
          </el-form-item>
        <!-- <el-form-item
          label="发布时间"
          prop="publishTime"
          style="padding-top: 40px"
        >
          <el-date-picker
            v-model="form.publishTime"
            align="right"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProject,
  getProject,
  delProject,
  addProject,
  updateProject,
  exportProject,
  upProject,
  uploadAvatar,
  downProject,
  publishProject
} from "@/api/tsystem/project";
import Editor from "@/components/Editor";

export default {
  name: "Project",
  components: {
    Editor,
  },
  data() {
    return {
      tableHeight: window.innerHeight - 500,

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工程项目简介表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        contentType: null,
        content: null,
        iconUrl: null,
        publishStatus: null,
        publishTime: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 项目工程状态数据字典
      publishStatusOptions: [],
      //项目工程类型数据字典
      contentTypeOptions: [],
      //查看大图图片地址
      dialogImageUrl: "",
      //查看大图弹出层显示隐藏标识
      dialogVisible: false,
      //已上传图片地址
      fileList: [],
      hideUpload: false,
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_project_status").then((response) => {
      this.publishStatusOptions = response.data;
    });
    this.getDicts("sys_project_type").then((response) => {
      this.contentTypeOptions = response.data;
    });
  },
  methods: {
    /** 查询工程项目简介列表 */
    getList() {
      this.loading = true;
      listProject(this.queryParams).then((response) => {
        this.projectList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.fileList = [];
      this.form = {
        id: null,
        title: null,
        contentType: null,
        content: null,
        iconUrl: null,
        publishStatus: null,
        publishTime: null,
        createbyId: null,
        createbyName: null,
        createTime: null,
        updatebyId: null,
        updatebyName: null,
        updateTime: null,
        delFlag: null,
        display:0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工程项目简介";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getProject(id).then((response) => {
        this.form = response.data;
        this.form.publishStatus=this.form.publishStatus+""
        this.open = true;
        this.title = "修改工程项目简介";
        let obj = new Object();
        obj.url = process.env.VUE_APP_BASE_API + this.form.iconUrl;
        this.fileList.push(obj);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateProject(this.form).then((response) => {
              if (response.code === 200) {
               this.msgSuccess("修改成功");
                this.reset();
                this.open = false;
                this.getList();
                this.hideUpload = false;
              }
            });
          } else {
            addProject(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("添加成功");
                this.reset();
                this.open = false;
                this.getList();
                this.hideUpload = false;
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        '是否确认删除该数据？',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delProject(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(function () {});
    },
    handleUp(row) {
      this.$confirm("是否上架该项目工程?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return upProject(row.id);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("上架成功");
        })
        .catch(function () {});
    },

    handlePublish(row) {
      this.$confirm('是否确认上架当前项目?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        return publishProject(row.id);
      }).then(() => {
        this.getList();
        this.msgSuccess("上架成功");
      }).catch(function() {});
    },

    // handleDown(row) {
    //   this.$confirm("是否下架该项目工程?", "警告", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   })
    //     .then(function () {
    //       return downProject(row.id);
    //     })
    //     .then(() => {
    //       this.getList();
    //       this.msgSuccess("发布成功");
    //     })
    //     .catch(function () {});
    // },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有工程项目简介数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportProject(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(function () {});
    },
    // 覆盖默认的上传行为
    requestUpload(val) {
      let formData = new FormData();
      formData.append("uploadfile", val.file);
      uploadAvatar(formData).then((response) => {
        if (response.code === 200) {
          this.form.iconUrl = response.imgUrl;
          this.msgSuccess("上传成功");
        }
      });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.form.iconUrl = "";
      this.hideUpload = fileList.length >= 1;
    },
    handlePictureCardPreview(file) {
      console.log(file);
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 上传预处理
    beforeUpload(file) {
      if (file.type.indexOf("image/") == -1) {
        this.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
        return false;
      }
    },
    //上传文件超出数量
    handleExceed(files, fileList) {
      this.msgError("最多上传1张图片");
    },
    handleChange(files, fileList) {
      this.hideUpload = fileList.length >= 1;
    },
  },
};
</script>
<style>
/*去除upload组件过渡效果*/
.el-upload-list__item {
  transition: none !important;
}
.hide .el-upload--picture-card {
  display: none;
}
.top {
  margin-left: 10px;
  margin-top: 20px;
}
.mb8 {
    margin-bottom: 20px !important;
}
.tableS{
  margin-top: 0px;
}

.editor {
    white-space: pre-wrap!important;
    line-height: normal !important;
    height: 208px;
    margin-top: -16px;
}
.editor {
    margin-top: -15px;
    white-space: pre-wrap!important;
    line-height: normal !important;
    height: 208px;
}
</style>
