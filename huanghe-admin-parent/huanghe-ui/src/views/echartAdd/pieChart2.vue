<template>
    <div :class="className" :style="{ height: height, width: width , top: top}" />
</template>


<script>
import resize from './mixins/resize'
import echarts from 'echarts'
import { getOrganUserType } from "@/api/homePage/homePage";

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '400px'
        },
        top: {
            type: String,
            // default: '20px'
        },
    },
    data() {
        return {
            servicedata: [],
            nameDatas: [],
            chart: null
        }
    },
    mounted() {
        this.getList();
        this.$nextTick(() => {
            this.initChart()
        });
        // 这将使图例在水平方向上居中

    },

    created() {
        // this.getList();
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },


    methods: {
        getList() {
            getOrganUserType().then((res) => {
                let arr = res.data;
                for (var i = 0; i < arr.length; i++) {
                    this.servicedata.push({ value: arr[i].value, name: arr[i].name });
                    this.nameDatas.push({ name: arr[i].name });
                }
                this.initChart();

                // this.series.data = res.data

            })

        },
        initChart() {
            this.chart = echarts.init(this.$el, 'macarons')

            this.chart.setOption({

                title: {
                    text: '机构用户类型',
                    // subtext: 'Fake Data',
                    left: 'left'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    right: 'right',
                    bottom: '20%',
                    // data: this.nameDatas,



                },

                label: {
                    show: true,
                    formatter(param) {
                        // correct the percentage
                        return param.name + ' (' + param.percent + '%)';
                    }
                },
                series: [
                    {
                        name: '',
                        type: 'pie',
                        radius: '50%',
                        data: this.servicedata,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]

            })
        }
    }
}
</script>

<style scoped></style>