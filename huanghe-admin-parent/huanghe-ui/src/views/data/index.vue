<template>
  <div class="app-container">

    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="文件名称" prop="dataPath">
        <el-input v-model="queryParams.dataPath" placeholder="请输入文件名称" clearable size="small" style="width: 200px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="解析状态" prop="dataStatus">
        <el-select v-model="queryParams.dataStatus" clearable placeholder="请选择解析状态">
          <el-option v-for="dict in dataStatusOptions" :key="dict.dictValue" :label="dict.dictLabel"
            :value="dict.dictValue" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置
        </el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['data:settle:add']">添加解析任务</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-plus" size="mini" @click="handleRunAll"
          v-hasPermi="['data:settle:remove']">执行全部</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['data:settle:remove']">批量删除
        </el-button>
      </el-col>
      <!-- <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar> -->
    </el-row>
    <div class="div_">
      <i class="el-icon-info" style="color: #078fea;margin: 8px;"></i><span style="color: #51575a;">已选择<span
          style="color: #078fea;margin: 4px;">{{ ids.length }}</span>项</span><el-button type="text" size="mini"
        style="margin: 15px;" @click="clear">清空</el-button>
    </div>
    <el-table v-loading="loading" :data="settleList" @selection-change="handleSelectionChange"
      :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"
      :header-row-style="{ height: '20px' }" height="calc(100vh - 320px)"
      :header-cell-style="{ background: '#f5f7fa', padding: '0px' }" ref="telTable" class="tableS">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column min-width="50" align="center" label="序号">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文件名称" align="center" prop="dataPath" min-width="150" show-overflow-tooltip />
      <el-table-column label="全文资源量" align="center" prop="fullCount" min-width="150" show-overflow-tooltip />
      <el-table-column label="PDF资源量" align="center" prop="pdfCount" min-width="100" show-overflow-tooltip />
      <el-table-column label="解析状态" align="center" prop="dataStatus" min-width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.dataStatus == '0'"> 未开始 </el-tag>
          <el-tag v-if="scope.row.dataStatus == '1'" :type="'info'">解析中</el-tag>
          <el-tag v-if="scope.row.dataStatus == '2'" :type="'success'">解析成功</el-tag>
          <el-tag v-if="scope.row.dataStatus == '3'" :type="'danger'">解析失败</el-tag>
          <el-tag v-if="scope.row.dataStatus == '4'" :type="'warning'">部分成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="解析开始时间" align="center" prop="parseStartTime" min-width="180" show-overflow-tooltip />
      <el-table-column label="解析结束时间" align="center" prop="parseEndTime" min-width="180" show-overflow-tooltip />
      <el-table-column label="描述" align="center" prop="dataJson" min-width="200" show-overflow-tooltip />
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="180" show-overflow-tooltip />
      <el-table-column label="上传成员" align="center" prop="createbyName" min-width="150" show-overflow-tooltip />
      <el-table-column label="备注" align="center" prop="dataRemark" min-width="200" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" min-width="250">
        <template slot-scope="scope">
          <!-- <el-button size="mini" v-if="scope.row.dataStatus == 3" icon="el-icon-thumb" type="text"
            v-hasPermi="['data:settle:run']" @click="handleUpdateStatus(scope.row)">再次解析
          </el-button> -->
          <el-button class="parseButton" size="mini" icon="el-icon-thumb" type="text" v-hasPermi="['data:settle:run']"
            @click="handleUpdateStatus(scope.row)" :disabled="scope.row.dataStatus !=0">解析
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-view" v-hasPermi="['data:log:list']"
            @click="handleLog(scope.row)">查看日志
          </el-button>
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="hanleUpdate(scope.row)"
            v-hasPermi=" ['data:settle:edit'] ">编辑
          </el-button> -->
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" size="mini"
            v-hasPermi="['data:settle:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <!-- 添加或修改数据入库配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="解析文件路径" prop="dataPath">
          <el-input v-model="form.dataPath" placeholder="解析文件路径" />
        </el-form-item>
        <!-- <el-form-item
          label="阅读模式"
          prop="readMode"
        >
          <el-select
            v-model=" form.readMode "
            placeholder="阅读模式"
          >
          <el-option
              label="简体带标点"
              value="O"
            ></el-option>
            <el-option
              label="简体"
              value="S"
            ></el-option>
            <el-option
              label="繁体"
              value="T"
            ></el-option>
            <el-option
              label="繁体带标点"
              value="P"
            ></el-option>
            <el-option
              label="图片"
              value="IM"
            ></el-option>
            <el-option
              label="版式还原"
              value="SR"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="描述" prop="dataRemark">
          <el-input v-model="form.dataJson" type="textarea" placeholder="描述" :rows="5" resize='none' />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 日志 -->
    <el-dialog :title="title" :visible.sync="logView" append-to-body :close-on-click-modal="false" width="75%"
      :before-close="handleDialogClose" style="height: 1000px;">
      <el-form ref="formLog" :model="queryLogParams" :rules="rules" :inline="true">
        <el-form-item label="题名" prop="fileName" label-width="100">
          <el-input v-model="queryLogParams.fileName" placeholder="请输入" size="small" @keyup.enter.native="queryLog" />
        </el-form-item>
        <el-form-item label="解析状态" prop="dataStatus" label-width="100">
          <el-select v-model="queryLogParams.dataStatus" placeholder="请选择解析状态" @keyup.enter.native="queryLog" clearable
            size="small">
            <el-option key="2" label="解析成功" value="2" />
            <el-option key="3" label="解析失败" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="cyan" icon="el-icon-search" size="mini" @click="queryLog">搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetLogView">重置
          </el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="cyan" icon="el-icon-download"
          size="mini" @click="handleExport">导出</el-button>
        </el-col>
      </el-row>
      <el-table v-loading="logloading" :data="logList" @selection-change="handleSelectionChange"
        :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '20px' }" height="calc(100vh - 380px)"
        :header-cell-style="{ background: '#f5f7fa', padding: '0px' }">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="题名" align="center" prop="fileName" width="400" />
        <el-table-column label="解析开始时间" align="center" prop="parseStartTime" width="180">
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.parseStartTime, "{y}-{m}-{d} {h}:{i}:{s}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="解析结束时间" align="center" prop="parseEndTime" width="180">
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.parseEndTime, "{y}-{m}-{d} {h}:{i}:{s}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="解析状态" align="center" prop="dataStatus">
          <template slot-scope="scope">
            <p v-if="logList[scope.$index].dataStatus == 1">解析中</p>
            <p v-if="logList[scope.$index].dataStatus == 2">解析成功</p>
            <p v-if="logList[scope.$index].dataStatus == 3">解析失败</p>
            <p v-if="logList[scope.$index].dataStatus == 4">部分成功</p>
          </template>
        </el-table-column>
        <el-table-column label="错误描述" align="center" prop="errorMsg" />
      </el-table>
      <pagination v-show="totalLog > 0" :total="totalLog" :page.sync="queryLogParams.pageNum"
        :limit.sync="queryLogParams.pageSize" @pagination="getLog" />
      <div slot="footer">
        <el-button @click="closeLogView">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  addSettle,
  delSettle,
  exportSettle,
  getSettle,
  listLog,
  listSettle,
  updateSettle,
  upStatus,
  startAll,
  exportLog,
} from "@/api/data/settle";

export default {
  name: "Settle",
  dicts: ["in_status", "data_status", "read_mode"],
  data() {
    return {
      tableHeight: window.innerHeight - 285,
      table: window.innerHeight - 410,
      // 遮罩层
      loading: true,
      logloading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      totalLog: 0,
      logList: [],
      // 数据入库配置表格数据
      settleList: [],
      formView: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      view: false,
      logView: false,
      // 查询参数

      queryLogParams: {
        pageNum: 1,
        pageSize: 10,
        fileName: null,
        dataStatus: null,
        taskId: "",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dataPath: null,
        dataStatus: null,
        dbCode: null,
      },
      // 状态数据字典
      inStatusOptions: [],
      // 状态数据字典
      dataStatusOptions: [],
      // 状态数据字典
      readModeOptions: [],
      //数据库分类
      dbCodeList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dataPath: [
          { required: true, message: "解析文件路径不能为空", trigger: "blur" },
        ],
      },
      taskId: null,
      currentName: null,
    };
  },
  destroyed: function () {
    this.saveSearchParam(this, this.currentName, this.queryParams);
  },
  created() {
    this.currentName = this.$route.name;
    this.getList();
    // this.getDicts("in_status").then((response) => {
    //   this.inStatusOptions = response.data;
    // });
    this.getDicts("data_status").then((response) => {
      this.dataStatusOptions = response.data;
    });
    // this.getDicts("read_mode").then((response) => {
    //   this.readModeOptions = response.data;
    // });
  },
  methods: {
    //关闭日志对话框是刷新列表
    handleDialogClose(done) {
      this.getList()
      done();
    },
    /** 查询数据入库配置列表 */
    getList() {
      this.loading = true;
      listSettle(this.queryParams).then((response) => {
        this.settleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        dataPath: null,
        dataStatus: null,
        parseStartTime: null,
        parseEndTime: null,
        dataRemark: null,
        createTime: null,
        dbCode: "R",
      };

      this.resetForm("form");
    },
    // 清除查看内容
    resetView() {
      this.formView = {
        id: null,
        dataPath: null,
        dataStatus: null,
        parseStartTime: null,
        parseEndTime: null,
        dataRemark: null,
        createTime: null,
      };
      this.resetForm("formView");
    },
    //清空选中的数据
    clear() {
      this.$refs.telTable.clearSelection(); // 取消选中
    },
    // 重置
    resetLogView() {
      this.formLog = {
        fileName: null,
      };
      this.resetForm("formLog");
      this.formLog.taskId = this.taskId;
      this.getLog();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      (this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        bookName: null,
        inStatus: null,
        dataStatus: null,
      }),
        this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据入库配置";
    },
    /**
     * 修改
     */
    hanleUpdate(row) {
      this.reset();
      const id = row.id;

      if (row.dataStatus == 1) {
        this.msgError("解析中，不可编辑");
        this.getList();
      } else {
        getSettle(id).then((response) => {
          this.open = true;
          this.form = response.data;
          this.title = "修改数据入库配置";
        });
      }
    },
    getLog() {
      this.logloading = true;
      listLog(this.queryLogParams).then((response) => {
        this.logList = response.rows;
        this.totalLog = response.total;
        this.logloading = false;
      });
    },

    // 查看日志信息
    handleLog(row) {
      this.queryLogParams.taskId = row.id;
      this.queryLogParams.orderByColumn = "parseStartTime";
      this.queryLogParams.isAsc = "desc";
      this.logloading = true;
      listLog(this.queryLogParams).then((response) => {
        this.logList = response.rows;
        this.logView = true;
        this.title = "查看日志";
        this.totalLog = response.total;
        this.logloading = false;
      });
    },
    /**
     * 日志搜索按钮
     */

    queryLog() {
      this.queryParams.pageNum = 1;
      this.getLog();
    },
    /**
     * 启动
     */
    handleUpdateStatus(row) {
      const id = row.id;
      //解析状态 0-准备 1-解析中  2-解析成功 3-解析失败
      if (row.dataStatus == "1") {
        this.msgError("解析中，不可启动");
      } else {
        upStatus(id).then((response) => {
          if (response.code == 200) {
            this.msgSuccess("启动成功");
            this.getList();
          }
        });
      }
    },

    handleRunAll() {
      this.$confirm("确认执行全部吗?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          //return delSettle(ids);
          startAll().then((response) => {
            if (response.code == 200) {
              this.msgSuccess("启动成功");
            }
          });
        })
        .catch(function () { });
    },
    // 关闭查看
    closeDia() {
      this.view = false;
      this.resetView();
    },
    closeLogView() {
      this.logView = false;
      this.resetLogView();
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.dbCode = "R";
          if (this.form.id != null) {
            updateSettle(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addSettle(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      //解析状态 0-准备 1-解析中  2-解析成功 3-解析失败
      if (row.dataStatus == "1") {
        this.msgError("解析中，不可删除");
        this.getList();
      } else {
        this.$confirm("确认删除吗?", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return delSettle(ids);
          })
          .then(() => {
            this.getList();
            this.msgSuccess("删除成功");
          })
          .catch(function () { });
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryLogParams;
      this.$confirm("是否确认导出所有数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportLog(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(function () { });
    },
  },
};
</script>
<style lang="scss" scoped>
.border {
  width: 200px;
  border: 1px solid #bfbfbf;
  height: 40px;
}

.discribe {
  width: 600px;
  border: 1px solid #bfbfbf;
  height: 100px;
}

.main {
  margin-top: 10px;
  margin-left: 20px;
}
</style>
<style lang="scss" scoped>
.el-dialog__footer {
  padding: 20px;
  padding-top: 52px;
  text-align: right;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.outside {
  width: 100%;
  height: 90%;
}

.top {
  margin-left: 10px;
  margin-top: 20px;
}

.middletb {
  margin-left: 5px;
  margin-top: 10px;
}

.middle {
  margin-top: 10px;
  width: 99%;
}

.tableS {
  margin-top: 0px;
}

.div_ {
  background: #e6f7ff;
  border: 1px solid #bae7ff;
  margin-bottom: 10px;
  height: 30px;
  border-radius: 4px;
  font-size: 12px;
  align-items: center;
  display: flex;
}

::v-deep .parseButton.is-disabled,
.parseButton.is-disabled:hover,
.parseButton.is-disabled:focus {
  color: #C0C4CC !important;
  background-image: none;
}
</style>
