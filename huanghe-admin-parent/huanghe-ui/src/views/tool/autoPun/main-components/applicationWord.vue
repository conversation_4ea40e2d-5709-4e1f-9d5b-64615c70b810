
<template>
   <div class="evaluate">
       <!-- <Icon type="ios-list-box-outline" /> -->
       <Icon type="ios-add-circle" size="16" style="color:#A5040D;" @click="clickApproved"/>
       <!-- <Button size="small" icon="ios-list-box-outline" type="primary" @click="clickApproved">提交</Button> -->
       <Modal v-model="isShow" title="申请字表单" :mask-closable="false" width="50%"  :transfer="true" v-if="initSuccess">    
            <Row :style="{ height: contentHeight+'px'}" style="overflow: auto;">
               <Row>
                  <span style="margin-right:10px;">申请字数</span>  <InputNumber :min="1" v-model="value" style="width:80%;"></InputNumber>

               </Row>
               <Row style="margin-top:10px;">
                   <span style="margin-right:10px;">申请原因</span>
                   <Input v-model="workRemark" type="textarea"  :autosize="{minRows: 5,maxRows: 6}" placeholder="" style="width:80%;" maxlength="50" show-word-limit/>
               </Row>
            </Row> 
            <div slot="footer">
                <Button type="primary" @click="submit">提交</Button>
                <Button type="text" @click="cancel">取消</Button>
            </div>  
       </Modal>   
   </div>
</template>
<script>
import {INTERVAL_TIME} from '@/api/libs/js/config';
import { 
   getPunctuateApplyQuota
} from "@/api/autoPun/index";
export default {
    data(){
        return{
           initSuccess:false,
           isShow:false,
           contentHeight:window.innerHeight-760,
           workRemark:"",
           value:0
          
        }
    },
    methods:{
       clickApproved(){
           this.initSuccess=true;
           this.isShow=true;
           this.value=0;
           this.workRemark="";
       },
       submit(){
           this.$Modal.confirm({
              title: "确认提交？",
              content: "您确认要提交？",
              onOk: () => {
                let parmas={
                    "number":this.value+"",
                    "content":this.workRemark
                }
                getPunctuateApplyQuota(JSON.stringify(parmas)).then(res=>{
                    if(res.success===true){
                        this.$Message.success( res.msg );
                        this.isShow=false;
                    }
                })
              }
            });
          
       },
       cancel(){
           this.isShow=false;
       }
    }
}
</script>