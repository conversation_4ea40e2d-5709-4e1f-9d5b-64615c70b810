import request from '@/utils/request'

// 查询资讯管理列表
export function listNews(query) {
  return request({
    url: '/tsystem/news/list',
    method: 'get',
    params: query
  })
}

// 查询资讯管理详细
export function getNews(id) {
  return request({
    url: '/tsystem/news/' + id,
    method: 'get'
  })
}

// 新增资讯管理
export function addNews(data) {
  return request({
    url: '/tsystem/news',
    method: 'post',
    data: data
  })
}

// 修改资讯管理
export function updateNews(data) {
  return request({
    url: '/tsystem/news',
    method: 'put',
    data: data
  })
}

// 删除资讯管理
export function delNews(id) {
  return request({
    url: '/tsystem/news/' + id,
    method: 'delete'
  })
}

// 导出资讯管理
export function exportNews(query) {
  return request({
    url: '/tsystem/news/export',
    method: 'get',
    params: query
  })
}

// 资讯海报上传
export function uploadAvatar(data) {
  return request({
    url: '/tsystem/news/upload',
    method: 'post',
    data: data
  })
}

// 发布资讯
export function publishNews(id) {
  return request({
    url: '/tsystem/news/publish/'+id,
    method: 'put'
  })
}
